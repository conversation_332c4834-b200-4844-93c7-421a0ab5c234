#!/usr/bin/env python3
"""
FITS文件背景抽取工具配置文件

修改这个文件中的参数来自定义处理行为
"""

# =============================================================================
# 路径配置
# =============================================================================

# 输入FITS文件目录
INPUT_DIRECTORY = r"E:\fix_data\star-detect"

# 输出目录 (None表示在输入目录下创建background_output文件夹)
OUTPUT_DIRECTORY = None

# =============================================================================
# 背景估计参数
# =============================================================================

# 2D背景估计的网格大小 (像素)
BACKGROUND_BOX_SIZE = 50

# 简化版网格背景估计的网格大小
SIMPLE_GRID_SIZE = 64

# Sigma-clipped统计的参数
SIGMA_CLIP_SIGMA = 3.0
SIGMA_CLIP_MAXITERS = 5

# 是否使用网格方法 (简化版)
USE_GRID_METHOD = True

# 最小图像尺寸，小于此尺寸将使用简单方法
MIN_SIZE_FOR_GRID = 128

# =============================================================================
# 输出配置
# =============================================================================

# JPG图像的DPI
JPG_DPI = 150

# JPG图像的尺寸 (英寸)
JPG_FIGURE_SIZE = (10, 10)

# 显示范围的百分位数
DISPLAY_PERCENTILES = [1, 99]

# 输出FITS文件的数据类型
OUTPUT_DTYPE = 'float32'

# =============================================================================
# 文件格式配置
# =============================================================================

# 支持的FITS文件扩展名
FITS_EXTENSIONS = ['.fits', '.fit', '.fts']

# 是否包含大写扩展名
INCLUDE_UPPERCASE_EXTENSIONS = True

# =============================================================================
# 日志配置
# =============================================================================

# 日志级别 ('DEBUG', 'INFO', 'WARNING', 'ERROR')
LOG_LEVEL = 'INFO'

# 日志文件名
LOG_FILENAME = 'background_extraction.log'

# 是否在控制台显示日志
CONSOLE_OUTPUT = True

# =============================================================================
# 高级参数
# =============================================================================

# photutils Background2D的滤波器大小
BACKGROUND_FILTER_SIZE = 3

# 是否在FITS头文件中添加处理历史
ADD_HISTORY_TO_HEADER = True

# 处理失败时是否继续处理其他文件
CONTINUE_ON_ERROR = True

# 是否覆盖已存在的输出文件
OVERWRITE_EXISTING = True

# =============================================================================
# 性能配置
# =============================================================================

# 是否启用并行处理 (实验性功能)
ENABLE_PARALLEL_PROCESSING = False

# 并行处理的进程数 (None表示使用CPU核心数)
PARALLEL_PROCESSES = None

# 大文件处理的内存限制 (MB)
MEMORY_LIMIT_MB = 1024

# =============================================================================
# 调试选项
# =============================================================================

# 是否保存中间结果
SAVE_INTERMEDIATE_RESULTS = False

# 是否显示详细的统计信息
VERBOSE_STATISTICS = True

# 是否在处理每个文件后显示内存使用情况
SHOW_MEMORY_USAGE = False
