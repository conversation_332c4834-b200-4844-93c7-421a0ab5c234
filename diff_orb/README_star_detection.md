# FITS文件星点检测工具

## 概述

`star_detection.py` 是一个专门用于检测FITS文件中星点的工具，使用SEP库进行高精度的星点检测。该工具能够：

- 检测FITS文件中的星点
- 按靠近图像中心程度和亮度对星点进行排序
- 输出详细的星点坐标数据文件
- 生成带有星点标注和序号的可视化JPG图像

## 功能特点

### 1. 高精度星点检测
- 使用SEP（Source Extractor Python）库进行星点检测
- 自动背景估计和减除
- 可调节的检测阈值和参数

### 2. 智能排序系统
- **中心距离权重**：优先选择靠近图像中心的星点
- **亮度权重**：优先选择较亮的星点
- **综合评分**：结合两个因素计算最终排序

### 3. 详细数据输出
输出的文本文件包含以下信息：
- 排序序号
- X、Y坐标（像素）
- 流量（亮度）
- 到图像中心的距离
- 综合分数
- 半长轴、半短轴
- 椭圆度

### 4. 可视化结果
- 生成高质量的JPG图像
- 用红色圆圈标注星点位置
- 黄色数字显示排序序号
- 支持自定义显示星点数量

## 安装依赖

确保已安装以下Python包：

```bash
pip install sep numpy matplotlib astropy
```

## 使用方法

### 基本用法

```bash
python star_detection.py <FITS文件路径>
```

### 示例

```bash
python star_detection.py "aligned_comparison_20250715_175203_difference.fits"
```

### 高级参数

```bash
python star_detection.py <FITS文件> [选项]

选项:
  -o, --output-dir DIR          输出目录（默认为FITS文件所在目录）
  -t, --threshold FLOAT         检测阈值（默认1.5）
  -a, --min-area INT           最小检测区域像素数（默认5）
  -cw, --center-weight FLOAT   中心距离权重（默认0.6）
  -bw, --brightness-weight FLOAT 亮度权重（默认0.4）
  -m, --max-display INT        最大显示星点数量（默认50）
  --deblend-nthresh INT        去混合阈值数量（默认32）
  --deblend-cont FLOAT         去混合连续性参数（默认0.005）
```

### 参数说明

- **检测阈值** (`-t`): 相对于背景噪声的倍数，值越小检测到的星点越多
- **中心距离权重** (`-cw`): 控制中心位置的重要性（0-1之间）
- **亮度权重** (`-bw`): 控制亮度的重要性（0-1之间）
- **最大显示数量** (`-m`): 在可视化图像中显示的星点数量

注意：中心距离权重 + 亮度权重 应该等于 1.0

## 输出文件

### 1. 星点数据文件 (`*_stars_*.txt`)

包含所有检测到的星点信息，按综合分数排序：

```
# 星点检测结果
# 图像尺寸: 4800 x 3211
# 检测到的星点数量: 4453
# 排序方式: 靠近图像中心程度 + 亮度
#
# 列说明:
# 排序序号, X坐标, Y坐标, 流量(亮度), 到中心距离, 综合分数, 半长轴, 半短轴, 椭圆度
#
  1,  1519.61,  3195.82,    2434.74,  1817.75, 0.6212, 927.69,   6.14, 0.9934
  2,  2397.60,  1602.70,       0.10,     3.69, 0.5992,   1.01,   0.47, 0.5323
  ...
```

### 2. 可视化图像文件 (`*_stars_*.jpg`)

- 灰度显示原始FITS图像
- 红色圆圈标注星点位置
- 黄色数字显示排序序号
- 包含检测统计信息

## 使用示例

### 示例1：默认参数检测

```bash
python star_detection.py "difference.fits"
```

### 示例2：调整排序权重

```bash
# 更重视中心位置
python star_detection.py "difference.fits" -cw 0.8 -bw 0.2

# 更重视亮度
python star_detection.py "difference.fits" -cw 0.3 -bw 0.7
```

### 示例3：调整检测灵敏度

```bash
# 更敏感的检测（检测更多暗星点）
python star_detection.py "difference.fits" -t 1.0

# 更严格的检测（只检测明亮星点）
python star_detection.py "difference.fits" -t 3.0
```

### 示例4：显示更多星点

```bash
python star_detection.py "difference.fits" -m 100
```

## 技术细节

### 检测算法
1. **背景估计**：使用SEP库自动估计图像背景
2. **背景减除**：从原图像中减去背景
3. **星点提取**：使用阈值检测和去混合算法
4. **形状分析**：计算每个星点的椭圆参数

### 排序算法
1. **中心距离标准化**：距离越近分数越高
2. **亮度标准化**：流量越大分数越高
3. **综合评分**：加权平均计算最终分数
4. **降序排列**：按综合分数从高到低排序

## 注意事项

1. **FITS文件格式**：支持标准FITS格式，自动处理3D数据（取第一个通道）
2. **内存使用**：大图像可能需要较多内存
3. **处理时间**：检测时间与图像大小和星点数量成正比
4. **参数调优**：根据具体图像特点调整检测参数以获得最佳效果

## 故障排除

### 常见问题

1. **检测到的星点太少**：降低检测阈值 (`-t`)
2. **检测到太多噪声**：提高检测阈值或增加最小区域 (`-a`)
3. **排序不理想**：调整中心距离和亮度权重
4. **中文显示问题**：确保系统安装了中文字体

### 错误处理

工具包含完整的错误处理机制，会在日志中显示详细的错误信息和处理状态。
