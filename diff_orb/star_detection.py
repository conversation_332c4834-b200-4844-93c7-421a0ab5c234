#!/usr/bin/env python3
"""
FITS文件星点检测工具
使用SEP库检测FITS文件中的星点，按靠近图像中心程度和亮度排序
输出星点坐标数据和可视化结果
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from astropy.io import fits
import sep
import logging
from datetime import datetime
import argparse
from scipy import ndimage
from skimage import morphology

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

class StarDetector:
    """
    星点检测器类
    使用SEP库检测FITS文件中的星点
    """
    
    def __init__(self, detection_threshold=5.0, min_area=10, deblend_nthresh=32, deblend_cont=0.005,
                 line_threshold_percentile=99.5):
        """
        初始化星点检测器
        
        Args:
            detection_threshold (float): 检测阈值（相对于背景噪声的倍数）
            min_area (int): 最小检测区域像素数
            deblend_nthresh (int): 去混合阈值数量
            deblend_cont (float): 去混合连续性参数
        """
        self.detection_threshold = detection_threshold
        self.min_area = min_area
        self.deblend_nthresh = deblend_nthresh
        self.deblend_cont = deblend_cont
        self.line_threshold_percentile = line_threshold_percentile
        
        # 设置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def load_fits_data(self, fits_path):
        """
        加载FITS文件数据
        
        Args:
            fits_path (str): FITS文件路径
            
        Returns:
            tuple: (图像数据, FITS头信息)，如果失败返回(None, None)
        """
        try:
            with fits.open(fits_path) as hdul:
                data = hdul[0].data.astype(np.float64)
                header = hdul[0].header
                
                # 处理可能的3D数据（取第一个通道）
                if len(data.shape) == 3:
                    data = data[0]
                
                self.logger.info(f"成功加载FITS文件: {os.path.basename(fits_path)}")
                self.logger.info(f"数据形状: {data.shape}, 数据范围: [{np.min(data):.6f}, {np.max(data):.6f}]")
                
                return data, header
                
        except Exception as e:
            self.logger.error(f"加载FITS文件失败 {fits_path}: {str(e)}")
            return None, None

    def remove_bright_lines(self, image_data, line_threshold_percentile=None,
                           morphology_size=3, min_line_length=50):
        """
        去除图像中的亮线

        Args:
            image_data (np.ndarray): 输入图像数据
            line_threshold_percentile (float): 亮线检测阈值百分位数
            morphology_size (int): 形态学操作的结构元素大小
            min_line_length (int): 最小线长度

        Returns:
            np.ndarray: 去除亮线后的图像
        """
        try:
            self.logger.info("开始去除图像中的亮线...")

            # 使用实例变量或参数中的阈值
            if line_threshold_percentile is None:
                line_threshold_percentile = self.line_threshold_percentile

            # 创建图像副本
            cleaned_image = image_data.copy()

            # 计算亮线检测阈值
            threshold = np.percentile(image_data, line_threshold_percentile)
            self.logger.info(f"亮线检测阈值: {threshold:.6f} (第{line_threshold_percentile}百分位数)")

            # 创建亮区域掩码
            bright_mask = image_data > threshold
            bright_pixel_count = np.sum(bright_mask)
            self.logger.info(f"检测到 {bright_pixel_count} 个亮像素")

            if bright_pixel_count == 0:
                self.logger.info("未检测到亮线")
                return cleaned_image

            # 使用形态学操作连接亮线
            # 水平线检测
            horizontal_kernel = np.ones((1, morphology_size * 3))
            horizontal_lines = ndimage.binary_opening(bright_mask, structure=horizontal_kernel)

            # 垂直线检测
            vertical_kernel = np.ones((morphology_size * 3, 1))
            vertical_lines = ndimage.binary_opening(bright_mask, structure=vertical_kernel)

            # 对角线检测
            diagonal_kernel1 = np.eye(morphology_size * 2)
            diagonal_lines1 = ndimage.binary_opening(bright_mask, structure=diagonal_kernel1)

            diagonal_kernel2 = np.fliplr(np.eye(morphology_size * 2))
            diagonal_lines2 = ndimage.binary_opening(bright_mask, structure=diagonal_kernel2)

            # 合并所有线检测结果
            line_mask = horizontal_lines | vertical_lines | diagonal_lines1 | diagonal_lines2

            # 进一步过滤：只保留足够长的线
            labeled_lines, num_lines = ndimage.label(line_mask)
            line_mask_filtered = np.zeros_like(line_mask)

            for i in range(1, num_lines + 1):
                line_pixels = labeled_lines == i
                if np.sum(line_pixels) >= min_line_length:
                    line_mask_filtered |= line_pixels

            # 扩展线掩码以确保完全去除
            expanded_mask = ndimage.binary_dilation(line_mask_filtered,
                                                   structure=np.ones((morphology_size, morphology_size)))

            # 去除检测到的线
            cleaned_image[expanded_mask] = 0

            removed_pixels = np.sum(expanded_mask)
            self.logger.info(f"去除了 {removed_pixels} 个像素 ({removed_pixels/image_data.size*100:.2f}%)")
            self.logger.info(f"检测到 {num_lines} 条线，其中 {np.sum(line_mask_filtered)} 个像素被标记为线")

            return cleaned_image

        except Exception as e:
            self.logger.error(f"去除亮线时出错: {str(e)}")
            return image_data  # 如果失败，返回原图像

    def detect_stars(self, image_data, remove_lines=True):
        """
        使用SEP库检测星点

        Args:
            image_data (np.ndarray): 图像数据
            remove_lines (bool): 是否去除亮线

        Returns:
            tuple: (检测到的星点信息数组, 背景对象)
        """
        try:
            # 确保数据是连续的并且是正确的字节序
            image_data = np.ascontiguousarray(image_data, dtype=np.float64)

            # 如果数据是大端字节序，转换为小端
            if image_data.dtype.byteorder == '>':
                image_data = image_data.byteswap().newbyteorder()

            # 去除亮线（如果启用）
            if remove_lines:
                image_data = self.remove_bright_lines(image_data)
            
            # 估计背景
            self.logger.info("估计图像背景...")
            bkg = sep.Background(image_data)
            self.logger.info(f"背景统计: 全局背景={bkg.globalback:.6f}, 全局RMS={bkg.globalrms:.6f}")
            
            # 从图像中减去背景
            image_sub = image_data - bkg
            
            # 检测星点
            # 计算实际阈值：背景RMS * 阈值倍数
            actual_threshold = bkg.globalrms * self.detection_threshold
            self.logger.info(f"开始星点检测，阈值倍数={self.detection_threshold}, 实际阈值={actual_threshold:.6f}...")

            # 增加SEP的像素缓冲区限制以处理大图像
            try:
                sep.set_extract_pixstack(1000000)  # 增加到100万像素
            except:
                pass  # 如果设置失败，使用默认值

            objects = sep.extract(
                image_sub,
                thresh=actual_threshold,  # 明确使用thresh参数
                err=bkg.globalrms,
                minarea=self.min_area,
                deblend_nthresh=self.deblend_nthresh,
                deblend_cont=self.deblend_cont
            )
            
            self.logger.info(f"检测到 {len(objects)} 个星点")
            
            return objects, bkg
            
        except Exception as e:
            self.logger.error(f"星点检测失败: {str(e)}")
            return None, None
    
    def calculate_center_distance(self, x, y, image_shape):
        """
        计算星点到图像中心的距离
        
        Args:
            x, y: 星点坐标
            image_shape: 图像形状 (height, width)
            
        Returns:
            float: 到中心的距离
        """
        center_y, center_x = image_shape[0] / 2, image_shape[1] / 2
        distance = np.sqrt((x - center_x)**2 + (y - center_y)**2)
        return distance
    
    def sort_stars(self, objects, image_shape, center_weight=0.6, brightness_weight=0.4):
        """
        按靠近图像中心程度和亮度对星点排序
        
        Args:
            objects: SEP检测到的星点对象
            image_shape: 图像形状
            center_weight (float): 中心距离权重
            brightness_weight (float): 亮度权重
            
        Returns:
            tuple: (排序后的索引, 排序分数)
        """
        if len(objects) == 0:
            return [], []
        
        # 计算到中心的距离
        center_distances = self.calculate_center_distance(objects['x'], objects['y'], image_shape)
        
        # 标准化距离（距离越小越好，所以用最大距离减去当前距离）
        max_distance = np.max(center_distances)
        normalized_center_scores = (max_distance - center_distances) / max_distance
        
        # 标准化亮度（flux越大越好）
        fluxes = objects['flux']
        max_flux = np.max(fluxes)
        min_flux = np.min(fluxes)
        if max_flux > min_flux:
            normalized_brightness_scores = (fluxes - min_flux) / (max_flux - min_flux)
        else:
            normalized_brightness_scores = np.ones_like(fluxes)
        
        # 计算综合分数
        composite_scores = (center_weight * normalized_center_scores + 
                          brightness_weight * normalized_brightness_scores)
        
        # 按分数降序排序
        sorted_indices = np.argsort(composite_scores)[::-1]
        
        self.logger.info(f"星点排序完成，权重设置: 中心距离={center_weight}, 亮度={brightness_weight}")

        return sorted_indices, composite_scores[sorted_indices]

    def save_star_data(self, objects, sorted_indices, composite_scores, image_shape, output_path):
        """
        保存星点坐标数据到文件

        Args:
            objects: SEP检测到的星点对象
            sorted_indices: 排序后的索引
            composite_scores: 综合分数
            image_shape: 图像形状
            output_path (str): 输出文件路径
        """
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("# 星点检测结果\n")
                f.write(f"# 图像尺寸: {image_shape[1]} x {image_shape[0]}\n")
                f.write(f"# 检测到的星点数量: {len(objects)}\n")
                f.write("# 排序方式: 靠近图像中心程度 + 亮度\n")
                f.write("#\n")
                f.write("# 列说明:\n")
                f.write("# 排序序号, X坐标, Y坐标, 流量(亮度), 到中心距离, 综合分数, 半长轴, 半短轴, 椭圆度\n")
                f.write("#\n")

                for i, idx in enumerate(sorted_indices):
                    obj = objects[idx]
                    center_dist = self.calculate_center_distance(obj['x'], obj['y'], image_shape)

                    # 计算椭圆度（如果a和b都存在）
                    ellipticity = 1.0 - (obj['b'] / obj['a']) if obj['a'] > 0 else 0.0

                    f.write(f"{i+1:3d}, {obj['x']:8.2f}, {obj['y']:8.2f}, {obj['flux']:10.2f}, "
                           f"{center_dist:8.2f}, {composite_scores[i]:6.4f}, "
                           f"{obj['a']:6.2f}, {obj['b']:6.2f}, {ellipticity:6.4f}\n")

            self.logger.info(f"星点数据已保存到: {output_path}")

        except Exception as e:
            self.logger.error(f"保存星点数据失败: {str(e)}")

    def create_visualization(self, image_data, objects, sorted_indices, output_path,
                           max_display=50, circle_radius=10):
        """
        创建星点可视化图像，用圆圈标注星点并配上序号

        Args:
            image_data (np.ndarray): 原始图像数据
            objects: SEP检测到的星点对象
            sorted_indices: 排序后的索引
            output_path (str): 输出JPG文件路径
            max_display (int): 最大显示星点数量
            circle_radius (float): 圆圈半径
        """
        try:
            # 创建图像显示
            fig, ax = plt.subplots(1, 1, figsize=(12, 12))

            # 显示图像（使用对数缩放以更好地显示动态范围）
            # 处理负值和零值
            display_data = image_data.copy()
            min_val = np.min(display_data)
            if min_val <= 0:
                display_data = display_data - min_val + 1e-10

            # 使用百分位数进行对比度调整
            vmin, vmax = np.percentile(display_data, [1, 99])

            im = ax.imshow(display_data, cmap='gray', origin='lower',
                          vmin=vmin, vmax=vmax)

            # 添加颜色条
            plt.colorbar(im, ax=ax, label='Pixel Value')

            # 限制显示的星点数量
            display_count = min(len(sorted_indices), max_display)

            # 标注星点
            for i in range(display_count):
                idx = sorted_indices[i]
                obj = objects[idx]

                # 绘制圆圈
                circle = patches.Circle((obj['x'], obj['y']), circle_radius,
                                      linewidth=2, edgecolor='red', facecolor='none')
                ax.add_patch(circle)

                # 添加序号标签
                ax.text(obj['x'] + circle_radius + 2, obj['y'] + circle_radius + 2,
                       str(i+1), color='yellow', fontsize=10, fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.2', facecolor='black', alpha=0.7))

            # 设置标题和标签
            ax.set_title(f'星点检测结果 (显示前{display_count}个星点)', fontsize=14, fontweight='bold')
            ax.set_xlabel('X 坐标 (像素)', fontsize=12)
            ax.set_ylabel('Y 坐标 (像素)', fontsize=12)

            # 添加图例
            legend_text = f"检测到 {len(objects)} 个星点\n显示前 {display_count} 个\n红圈: 星点位置\n黄色数字: 排序序号"
            ax.text(0.02, 0.98, legend_text, transform=ax.transAxes,
                   verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

            # 保存图像
            plt.tight_layout()
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            plt.close()

            self.logger.info(f"可视化图像已保存到: {output_path}")

        except Exception as e:
            self.logger.error(f"创建可视化图像失败: {str(e)}")

    def process_fits_file(self, fits_path, output_dir=None, center_weight=0.6,
                         brightness_weight=0.4, max_display=50, remove_lines=True):
        """
        处理FITS文件，检测星点并生成输出

        Args:
            fits_path (str): FITS文件路径
            output_dir (str): 输出目录，如果为None则使用FITS文件所在目录
            center_weight (float): 中心距离权重
            brightness_weight (float): 亮度权重
            max_display (int): 最大显示星点数量
            remove_lines (bool): 是否去除亮线

        Returns:
            dict: 处理结果摘要
        """
        try:
            self.logger.info("=" * 60)
            self.logger.info("开始FITS文件星点检测")
            self.logger.info("=" * 60)

            # 1. 加载FITS文件
            self.logger.info(f"加载FITS文件: {fits_path}")
            image_data, header = self.load_fits_data(fits_path)

            if image_data is None:
                self.logger.error("FITS文件加载失败")
                return None

            # 2. 检测星点
            self.logger.info("开始星点检测...")
            objects, bkg = self.detect_stars(image_data, remove_lines=remove_lines)

            if objects is None or len(objects) == 0:
                self.logger.warning("未检测到任何星点")
                return {"star_count": 0, "files_created": []}

            # 3. 排序星点
            self.logger.info("对星点进行排序...")
            sorted_indices, composite_scores = self.sort_stars(
                objects, image_data.shape, center_weight, brightness_weight
            )

            # 4. 准备输出文件路径
            if output_dir is None:
                output_dir = os.path.dirname(fits_path)

            os.makedirs(output_dir, exist_ok=True)

            # 生成输出文件名
            base_name = os.path.splitext(os.path.basename(fits_path))[0]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            data_output_path = os.path.join(output_dir, f"{base_name}_stars_{timestamp}.txt")
            viz_output_path = os.path.join(output_dir, f"{base_name}_stars_{timestamp}.jpg")

            # 5. 保存星点数据
            self.logger.info("保存星点坐标数据...")
            self.save_star_data(objects, sorted_indices, composite_scores,
                              image_data.shape, data_output_path)

            # 6. 创建可视化
            self.logger.info("创建可视化图像...")
            self.create_visualization(image_data, objects, sorted_indices,
                                    viz_output_path, max_display)

            # 7. 生成处理摘要
            result_summary = {
                "star_count": len(objects),
                "displayed_count": min(len(objects), max_display),
                "image_shape": image_data.shape,
                "detection_params": {
                    "threshold": self.detection_threshold,
                    "min_area": self.min_area,
                    "deblend_nthresh": self.deblend_nthresh,
                    "deblend_cont": self.deblend_cont
                },
                "sorting_weights": {
                    "center_weight": center_weight,
                    "brightness_weight": brightness_weight
                },
                "files_created": [data_output_path, viz_output_path],
                "background_stats": {
                    "global_back": float(bkg.globalback),
                    "global_rms": float(bkg.globalrms)
                }
            }

            self.logger.info("=" * 60)
            self.logger.info("星点检测完成")
            self.logger.info(f"检测到星点数量: {len(objects)}")
            self.logger.info(f"显示星点数量: {min(len(objects), max_display)}")
            self.logger.info(f"数据文件: {data_output_path}")
            self.logger.info(f"可视化文件: {viz_output_path}")
            self.logger.info("=" * 60)

            return result_summary

        except Exception as e:
            self.logger.error(f"处理FITS文件时出错: {str(e)}")
            return None


def main():
    """主函数，处理命令行参数"""
    parser = argparse.ArgumentParser(description='FITS文件星点检测工具')
    parser.add_argument('fits_file', help='输入的FITS文件路径')
    parser.add_argument('-o', '--output-dir', help='输出目录（默认为FITS文件所在目录）')
    parser.add_argument('-t', '--threshold', type=float, default=5.0,
                       help='检测阈值（相对于背景噪声的倍数，默认5.0，值越大检测到的星点越少）')
    parser.add_argument('-a', '--min-area', type=int, default=10,
                       help='最小检测区域像素数（默认10，值越大检测到的星点越少）')
    parser.add_argument('-cw', '--center-weight', type=float, default=0.6,
                       help='中心距离权重（默认0.6）')
    parser.add_argument('-bw', '--brightness-weight', type=float, default=0.4,
                       help='亮度权重（默认0.4）')
    parser.add_argument('-m', '--max-display', type=int, default=50,
                       help='最大显示星点数量（默认50）')
    parser.add_argument('--deblend-nthresh', type=int, default=32,
                       help='去混合阈值数量（默认32）')
    parser.add_argument('--deblend-cont', type=float, default=0.005,
                       help='去混合连续性参数（默认0.005）')
    parser.add_argument('--no-remove-lines', action='store_true',
                       help='禁用亮线去除功能')
    parser.add_argument('--line-threshold', type=float, default=99.5,
                       help='亮线检测阈值百分位数（默认99.5）')

    args = parser.parse_args()

    # 检查输入文件是否存在
    if not os.path.exists(args.fits_file):
        print(f"错误: FITS文件不存在: {args.fits_file}")
        sys.exit(1)

    # 检查权重参数
    if abs(args.center_weight + args.brightness_weight - 1.0) > 0.001:
        print(f"警告: 中心距离权重({args.center_weight}) + 亮度权重({args.brightness_weight}) != 1.0")
        print("将自动标准化权重...")
        total_weight = args.center_weight + args.brightness_weight
        args.center_weight /= total_weight
        args.brightness_weight /= total_weight
        print(f"标准化后权重: 中心距离={args.center_weight:.3f}, 亮度={args.brightness_weight:.3f}")

    # 创建星点检测器
    detector = StarDetector(
        detection_threshold=args.threshold,
        min_area=args.min_area,
        deblend_nthresh=args.deblend_nthresh,
        deblend_cont=args.deblend_cont,
        line_threshold_percentile=args.line_threshold
    )

    # 处理FITS文件
    result = detector.process_fits_file(
        args.fits_file,
        output_dir=args.output_dir,
        center_weight=args.center_weight,
        brightness_weight=args.brightness_weight,
        max_display=args.max_display,
        remove_lines=not args.no_remove_lines
    )

    if result is None:
        print("星点检测失败")
        sys.exit(1)
    else:
        print(f"\n星点检测成功完成!")
        print(f"检测到 {result['star_count']} 个星点")
        print(f"输出文件:")
        for file_path in result['files_created']:
            print(f"  - {file_path}")


if __name__ == "__main__":
    main()
