2025-07-23 14:32:09,627 - INFO - ============================================================
2025-07-23 14:32:09,627 - INFO - 开始<PERSON>hresher处理
2025-07-23 14:32:09,628 - INFO - ============================================================
2025-07-23 14:32:09,628 - INFO - 步骤1: 加载差异图像
2025-07-23 14:32:09,697 - INFO - 成功加载FITS文件: E:\github\local_kats\fits_dia\test_data\aligned_comparison_20250715_175203_difference.fits
2025-07-23 14:32:09,698 - INFO - 图像尺寸: (3211, 4800)
2025-07-23 14:32:09,713 - INFO - 数据范围: [0.000000, 0.977139]
2025-07-23 14:32:09,713 - INFO - 步骤2: 估计背景统计特性
2025-07-23 14:32:13,450 - INFO - 背景统计: mean=0.355420, median=0.354700, std=0.020954
2025-07-23 14:32:13,453 - INFO - 鲁棒统计: mad=0.018720, background=0.339696
2025-07-23 14:32:13,453 - INFO - 步骤3: 拟合TheThresher统计模型
2025-07-23 14:32:13,453 - INFO - 拟合TheThresher统计模型...
2025-07-23 14:32:43,862 - WARNING - 贝叶斯拟合失败，使用简单模型
2025-07-23 14:32:43,982 - INFO - 模型拟合完成: {'type': 'simple', 'mean': 0.34758207439336164, 'std': 0.04235287111277259, 'threshold': 0.47464068773167944}
2025-07-23 14:32:43,992 - INFO - 步骤4: 应用TheThresher算法
2025-07-23 14:32:43,992 - INFO - 应用TheThresher算法...
2025-07-23 14:32:45,191 - INFO - TheThresher处理完成:
2025-07-23 14:32:45,191 - INFO -   检测像素: 6,993 个 (0.045%)
2025-07-23 14:32:45,207 - INFO -   显著性范围: [-8.207, 14.865]
2025-07-23 14:32:45,207 - INFO - 步骤5: 检测显著源
2025-07-23 14:32:51,071 - INFO - 检测到 80 个显著源
2025-07-23 14:32:51,078 - INFO - 步骤6: 保存结果
2025-07-23 14:32:51,201 - INFO - FITS结果已保存: E:\github\local_kats\fits_dia\test_data\davidhogg_thresher_20250723_143251_processed.fits
2025-07-23 14:32:51,325 - INFO - FITS结果已保存: E:\github\local_kats\fits_dia\test_data\davidhogg_thresher_20250723_143251_significance.fits
2025-07-23 14:32:51,331 - INFO - 源目录已保存: E:\github\local_kats\fits_dia\test_data\davidhogg_thresher_20250723_143251_sources.txt
2025-07-23 14:32:59,131 - INFO - 可视化结果已保存: E:\github\local_kats\fits_dia\test_data\davidhogg_thresher_20250723_143251_visualization.png
2025-07-23 14:32:59,131 - INFO - TheThresher处理完成，检测到 80 个显著源
2025-07-23 14:42:04,098 - INFO - ============================================================
2025-07-23 14:42:04,099 - INFO - 开始David Hogg TheThresher处理
2025-07-23 14:42:04,099 - INFO - ============================================================
2025-07-23 14:42:04,099 - INFO - 步骤1: 加载差异图像
2025-07-23 14:42:04,155 - INFO - 成功加载FITS文件: ../test_data/aligned_comparison_20250715_175203_difference.fits
2025-07-23 14:42:04,155 - INFO - 图像尺寸: (3211, 4800)
2025-07-23 14:42:04,172 - INFO - 数据范围: [0.000000, 0.977139]
2025-07-23 14:42:04,172 - INFO - 步骤2: 估计背景统计特性
2025-07-23 14:42:07,623 - INFO - 背景统计: mean=0.355420, median=0.354700, std=0.020954
2025-07-23 14:42:07,623 - INFO - 鲁棒统计: mad=0.018720, background=0.339696
2025-07-23 14:42:07,623 - INFO - 步骤3: 拟合TheThresher统计模型
2025-07-23 14:42:07,623 - INFO - 拟合TheThresher统计模型...
2025-07-23 14:42:36,273 - WARNING - 贝叶斯拟合失败，使用简单模型
2025-07-23 14:42:36,393 - INFO - 模型拟合完成: {'type': 'simple', 'mean': 0.34758207439336164, 'std': 0.04235287111277259, 'threshold': 0.47464068773167944}
2025-07-23 14:42:36,402 - INFO - 步骤4: 应用TheThresher算法
2025-07-23 14:42:36,402 - INFO - 应用TheThresher算法...
2025-07-23 14:42:37,578 - INFO - TheThresher处理完成:
2025-07-23 14:42:37,578 - INFO -   检测像素: 6,993 个 (0.045%)
2025-07-23 14:42:37,593 - INFO -   显著性范围: [-8.207, 14.865]
2025-07-23 14:42:37,593 - INFO - 步骤5: 检测显著源
2025-07-23 14:42:43,365 - INFO - 检测到 80 个显著源
2025-07-23 14:42:43,372 - INFO - 步骤6: 保存结果
2025-07-23 14:42:43,494 - INFO - FITS结果已保存: ../test_data\davidhogg_thresher_20250723_144243_processed.fits
2025-07-23 14:42:43,605 - INFO - FITS结果已保存: ../test_data\davidhogg_thresher_20250723_144243_significance.fits
2025-07-23 14:42:43,612 - INFO - 源目录已保存: ../test_data\davidhogg_thresher_20250723_144243_sources.txt
2025-07-23 14:42:51,174 - INFO - 可视化结果已保存: ../test_data\davidhogg_thresher_20250723_144243_visualization.png
2025-07-23 14:42:51,174 - INFO - TheThresher处理完成，检测到 80 个显著源
2025-07-23 16:16:25,139 - INFO - ============================================================
2025-07-23 16:16:25,141 - INFO - 开始David Hogg TheThresher处理
2025-07-23 16:16:25,141 - INFO - ============================================================
2025-07-23 16:16:25,141 - INFO - 步骤1: 加载差异图像
2025-07-23 16:16:25,198 - INFO - 成功加载FITS文件: E:\github\local_kats\fits_dia\test_data\aligned_comparison_20250715_175203_difference.fits
2025-07-23 16:16:25,198 - INFO - 图像尺寸: (3211, 4800)
2025-07-23 16:16:25,213 - INFO - 数据范围: [0.000000, 0.977139]
2025-07-23 16:16:25,213 - INFO - 步骤2: 估计背景统计特性
2025-07-23 16:16:28,971 - INFO - 背景统计: mean=0.355420, median=0.354700, std=0.020954
2025-07-23 16:16:28,971 - INFO - 鲁棒统计: mad=0.018720, background=0.339696
2025-07-23 16:16:28,971 - INFO - 步骤3: 拟合TheThresher统计模型
2025-07-23 16:16:28,971 - INFO - 拟合TheThresher统计模型...
2025-07-23 16:17:00,271 - WARNING - 贝叶斯拟合失败，使用简单模型
2025-07-23 16:17:00,401 - INFO - 模型拟合完成: {'type': 'simple', 'mean': 0.34758207439336164, 'std': 0.04235287111277259, 'threshold': 0.47464068773167944}
2025-07-23 16:17:00,411 - INFO - 步骤4: 应用TheThresher算法
2025-07-23 16:17:00,412 - INFO - 应用TheThresher算法...
2025-07-23 16:17:01,649 - INFO - TheThresher处理完成:
2025-07-23 16:17:01,649 - INFO -   检测像素: 6,993 个 (0.045%)
2025-07-23 16:17:01,667 - INFO -   显著性范围: [-8.207, 14.865]
2025-07-23 16:17:01,667 - INFO - 步骤5: 检测显著源
2025-07-23 16:17:07,589 - INFO - 检测到 80 个显著源
2025-07-23 16:17:07,596 - INFO - 步骤6: 保存结果
2025-07-23 16:17:07,719 - INFO - FITS结果已保存: E:\github\local_kats\fits_dia\test_data\davidhogg_thresher_20250723_161707_processed.fits
2025-07-23 16:17:07,840 - INFO - FITS结果已保存: E:\github\local_kats\fits_dia\test_data\davidhogg_thresher_20250723_161707_significance.fits
2025-07-23 16:17:07,847 - INFO - 源目录已保存: E:\github\local_kats\fits_dia\test_data\davidhogg_thresher_20250723_161707_sources.txt
2025-07-23 16:17:07,885 - INFO - 标记 80 个源，面积范围: 5 - 4555 像素
2025-07-23 16:17:08,513 - INFO - 完成源标记，圆圈半径范围: 3 - 20 像素
2025-07-23 16:17:08,636 - INFO - 标记FITS文件已保存: E:\github\local_kats\fits_dia\test_data\davidhogg_thresher_20250723_161707_marked.fits
2025-07-23 16:17:16,537 - INFO - 可视化结果已保存: E:\github\local_kats\fits_dia\test_data\davidhogg_thresher_20250723_161707_visualization.png
2025-07-23 16:17:16,537 - INFO - TheThresher处理完成，检测到 80 个显著源
2025-07-23 16:20:44,410 - INFO - ============================================================
2025-07-23 16:20:44,411 - INFO - 开始David Hogg TheThresher处理
2025-07-23 16:20:44,411 - INFO - ============================================================
2025-07-23 16:20:44,411 - INFO - 步骤1: 加载差异图像
2025-07-23 16:20:44,466 - INFO - 成功加载FITS文件: ../test_data/aligned_comparison_20250715_175203_difference.fits
2025-07-23 16:20:44,466 - INFO - 图像尺寸: (3211, 4800)
2025-07-23 16:20:44,481 - INFO - 数据范围: [0.000000, 0.977139]
2025-07-23 16:20:44,481 - INFO - 步骤2: 估计背景统计特性
2025-07-23 16:20:48,062 - INFO - 背景统计: mean=0.355420, median=0.354700, std=0.020954
2025-07-23 16:20:48,063 - INFO - 鲁棒统计: mad=0.018720, background=0.339696
2025-07-23 16:20:48,063 - INFO - 步骤3: 拟合TheThresher统计模型
2025-07-23 16:20:48,063 - INFO - 拟合TheThresher统计模型...
2025-07-23 16:21:18,530 - WARNING - 贝叶斯拟合失败，使用简单模型
2025-07-23 16:21:18,654 - INFO - 模型拟合完成: {'type': 'simple', 'mean': 0.34758207439336164, 'std': 0.04235287111277259, 'threshold': 0.47464068773167944}
2025-07-23 16:21:18,665 - INFO - 步骤4: 应用TheThresher算法
2025-07-23 16:21:18,665 - INFO - 应用TheThresher算法...
2025-07-23 16:21:19,878 - INFO - TheThresher处理完成:
2025-07-23 16:21:19,878 - INFO -   检测像素: 6,993 个 (0.045%)
2025-07-23 16:21:19,893 - INFO -   显著性范围: [-8.207, 14.865]
2025-07-23 16:21:19,894 - INFO - 步骤5: 检测显著源
2025-07-23 16:21:26,009 - INFO - 检测到 80 个显著源
2025-07-23 16:21:26,019 - INFO - 步骤6: 保存结果
2025-07-23 16:21:26,139 - INFO - FITS结果已保存: ../test_data\davidhogg_thresher_20250723_162126_processed.fits
2025-07-23 16:21:26,258 - INFO - FITS结果已保存: ../test_data\davidhogg_thresher_20250723_162126_significance.fits
2025-07-23 16:21:26,266 - INFO - 源目录已保存: ../test_data\davidhogg_thresher_20250723_162126_sources.txt
2025-07-23 16:21:26,300 - INFO - 标记 80 个源，面积范围: 5 - 4555 像素
2025-07-23 16:21:26,983 - INFO - 完成源标记，圆圈半径范围: 3 - 20 像素
2025-07-23 16:21:27,098 - INFO - 标记FITS文件已保存: ../test_data\davidhogg_thresher_20250723_162126_marked.fits
2025-07-23 16:21:35,022 - INFO - 可视化结果已保存: ../test_data\davidhogg_thresher_20250723_162126_visualization.png
2025-07-23 16:21:35,022 - INFO - TheThresher处理完成，检测到 80 个显著源
