#!/usr/bin/env python3
"""
<PERSON> TheThresher Implementation
基于<PERSON>等人的TheThresher方法的天文图像处理实现

TheThresher是一种"幸运成像"(Lucky Imaging)技术，通过统计建模和
贝叶斯推理从多帧或单帧图像中提取高质量的天文信息。

Reference: <PERSON> et al. (2022), "The Thresher: Lucky imaging without the waste"
Monthly Notices of the Royal Astronomical Society, 511, 5372-5384

Author: Augment Agent
Date: 2025-07-23
"""

import os
import sys
import numpy as np
import logging
from datetime import datetime
from astropy.io import fits
from astropy.stats import sigma_clipped_stats, mad_std
from astropy.convolution import Gaussian2DKernel, convolve
from photutils import DAOStarFinder, aperture_photometry, CircularAperture
from scipy import ndimage, optimize
from scipy.stats import poisson, gamma
import matplotlib.pyplot as plt
from matplotlib.colors import LogNorm
import cv2


class DavidHoggThresher:
    """
    <PERSON>gg TheThresher实现
    
    TheThresher方法的核心思想：
    1. 统计建模 - 使用泊松-伽马混合模型描述像素强度分布
    2. 贝叶斯推理 - 通过最大似然估计提取真实信号
    3. 自适应阈值 - 基于统计显著性的动态阈值
    4. 鲁棒估计 - 对噪声和异常值的鲁棒处理
    """
    
    def __init__(self, significance_threshold=3.0, use_bayesian_inference=True):
        """
        初始化TheThresher处理器
        
        Args:
            significance_threshold (float): 统计显著性阈值
            use_bayesian_inference (bool): 是否使用贝叶斯推理
        """
        self.significance_threshold = significance_threshold
        self.use_bayesian_inference = use_bayesian_inference
        self.setup_logging()
        
        # TheThresher参数
        self.thresher_params = {
            'gamma_shape': 2.0,          # 伽马分布形状参数
            'gamma_scale': 1.0,          # 伽马分布尺度参数
            'poisson_rate': 1.0,         # 泊松分布率参数
            'convergence_tol': 1e-6,     # 收敛容差
            'max_iterations': 100,       # 最大迭代次数
            'kernel_size': 5,            # 卷积核大小
            'background_percentile': 25, # 背景估计百分位数
        }
        
    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('davidhogg_thresher.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def load_fits_image(self, fits_path):
        """
        加载FITS图像
        
        Args:
            fits_path (str): FITS文件路径
            
        Returns:
            tuple: (图像数据, 头信息)
        """
        try:
            with fits.open(fits_path) as hdul:
                data = hdul[0].data.astype(np.float64)
                header = hdul[0].header
                
            # 处理可能的3D数据
            if len(data.shape) == 3:
                data = data[0]
                
            self.logger.info(f"成功加载FITS文件: {fits_path}")
            self.logger.info(f"图像尺寸: {data.shape}")
            self.logger.info(f"数据范围: [{np.min(data):.6f}, {np.max(data):.6f}]")
            
            return data, header
            
        except Exception as e:
            self.logger.error(f"加载FITS文件失败 {fits_path}: {str(e)}")
            return None, None
            
    def estimate_background_statistics(self, image_data):
        """
        估计背景统计特性
        
        Args:
            image_data (np.ndarray): 图像数据
            
        Returns:
            dict: 背景统计信息
        """
        try:
            # 使用sigma-clipped统计
            mean, median, std = sigma_clipped_stats(image_data, sigma=3.0, maxiters=5)
            
            # 使用MAD估计鲁棒标准差
            mad = mad_std(image_data)
            
            # 估计背景水平（使用较低百分位数）
            background_level = np.percentile(image_data, self.thresher_params['background_percentile'])
            
            # 计算偏度和峰度
            skewness = self._calculate_skewness(image_data, mean, std)
            kurtosis = self._calculate_kurtosis(image_data, mean, std)
            
            stats = {
                'mean': mean,
                'median': median,
                'std': std,
                'mad': mad,
                'background_level': background_level,
                'skewness': skewness,
                'kurtosis': kurtosis,
                'min_value': np.min(image_data),
                'max_value': np.max(image_data)
            }
            
            self.logger.info(f"背景统计: mean={mean:.6f}, median={median:.6f}, std={std:.6f}")
            self.logger.info(f"鲁棒统计: mad={mad:.6f}, background={background_level:.6f}")
            
            return stats
            
        except Exception as e:
            self.logger.error(f"背景统计估计失败: {str(e)}")
            return None
            
    def _calculate_skewness(self, data, mean, std):
        """计算偏度"""
        if std == 0:
            return 0
        return np.mean(((data - mean) / std) ** 3)
        
    def _calculate_kurtosis(self, data, mean, std):
        """计算峰度"""
        if std == 0:
            return 0
        return np.mean(((data - mean) / std) ** 4) - 3
        
    def fit_statistical_model(self, image_data, background_stats):
        """
        拟合统计模型（泊松-伽马混合模型）
        
        Args:
            image_data (np.ndarray): 图像数据
            background_stats (dict): 背景统计信息
            
        Returns:
            dict: 拟合的模型参数
        """
        try:
            self.logger.info("拟合TheThresher统计模型...")
            
            # 准备数据
            data_flat = image_data.flatten()
            
            # 移除极端值
            data_clean = data_flat[
                (data_flat > background_stats['min_value']) & 
                (data_flat < np.percentile(data_flat, 99))
            ]
            
            # 初始参数估计
            initial_gamma_shape = self.thresher_params['gamma_shape']
            initial_gamma_scale = background_stats['std'] ** 2 / background_stats['mean']
            initial_poisson_rate = background_stats['mean']
            
            # 使用最大似然估计拟合参数
            if self.use_bayesian_inference:
                model_params = self._fit_bayesian_model(data_clean, initial_gamma_shape, 
                                                      initial_gamma_scale, initial_poisson_rate)
            else:
                model_params = self._fit_simple_model(data_clean, background_stats)
                
            self.logger.info(f"模型拟合完成: {model_params}")
            
            return model_params
            
        except Exception as e:
            self.logger.error(f"统计模型拟合失败: {str(e)}")
            return None
            
    def _fit_bayesian_model(self, data, init_shape, init_scale, init_rate):
        """拟合贝叶斯模型"""
        
        def negative_log_likelihood(params):
            shape, scale, rate = params
            if shape <= 0 or scale <= 0 or rate <= 0:
                return np.inf
                
            try:
                # 泊松-伽马混合模型的对数似然
                log_likelihood = np.sum(
                    poisson.logpmf(data, rate) + 
                    gamma.logpdf(rate, a=shape, scale=scale)
                )
                return -log_likelihood
            except:
                return np.inf
                
        # 优化参数
        initial_params = [init_shape, init_scale, init_rate]
        bounds = [(0.1, 10), (0.01, 100), (0.01, 1000)]
        
        try:
            result = optimize.minimize(negative_log_likelihood, initial_params, 
                                     bounds=bounds, method='L-BFGS-B')
            
            if result.success:
                shape, scale, rate = result.x
                return {
                    'type': 'bayesian',
                    'gamma_shape': shape,
                    'gamma_scale': scale,
                    'poisson_rate': rate,
                    'log_likelihood': -result.fun
                }
            else:
                self.logger.warning("贝叶斯拟合失败，使用简单模型")
                return self._fit_simple_model(data, {'mean': np.mean(data), 'std': np.std(data)})
                
        except Exception as e:
            self.logger.warning(f"贝叶斯拟合异常: {e}，使用简单模型")
            return self._fit_simple_model(data, {'mean': np.mean(data), 'std': np.std(data)})
            
    def _fit_simple_model(self, data, background_stats):
        """拟合简单统计模型"""
        return {
            'type': 'simple',
            'mean': background_stats['mean'],
            'std': background_stats['std'],
            'threshold': background_stats['mean'] + self.significance_threshold * background_stats['std']
        }

    def apply_thresher_algorithm(self, image_data, model_params, background_stats):
        """
        应用TheThresher算法进行图像处理

        Args:
            image_data (np.ndarray): 输入图像数据
            model_params (dict): 统计模型参数
            background_stats (dict): 背景统计信息

        Returns:
            tuple: (处理后图像, 显著性图像, 检测掩码)
        """
        try:
            self.logger.info("应用TheThresher算法...")

            # 1. 创建显著性图像
            significance_map = self._create_significance_map(image_data, model_params, background_stats)

            # 2. 应用自适应阈值
            detection_mask = significance_map > self.significance_threshold

            # 3. 形态学处理去除噪声
            detection_mask = self._morphological_filtering(detection_mask)

            # 4. 创建处理后的图像
            processed_image = self._create_processed_image(image_data, detection_mask, background_stats)

            # 5. 统计结果
            detected_pixels = np.sum(detection_mask)
            total_pixels = image_data.size
            detection_rate = detected_pixels / total_pixels * 100

            self.logger.info(f"TheThresher处理完成:")
            self.logger.info(f"  检测像素: {detected_pixels:,} 个 ({detection_rate:.3f}%)")
            self.logger.info(f"  显著性范围: [{np.min(significance_map):.3f}, {np.max(significance_map):.3f}]")

            return processed_image, significance_map, detection_mask

        except Exception as e:
            self.logger.error(f"TheThresher算法应用失败: {str(e)}")
            return None, None, None

    def _create_significance_map(self, image_data, model_params, background_stats):
        """创建统计显著性图像"""

        if model_params['type'] == 'bayesian':
            # 使用贝叶斯模型计算显著性
            gamma_shape = model_params['gamma_shape']
            gamma_scale = model_params['gamma_scale']
            poisson_rate = model_params['poisson_rate']

            # 计算每个像素的后验概率
            expected_background = gamma_shape * gamma_scale
            significance_map = (image_data - expected_background) / np.sqrt(expected_background + poisson_rate)

        else:
            # 使用简单模型计算显著性
            mean = model_params['mean']
            std = model_params['std']
            significance_map = (image_data - mean) / std

        return significance_map

    def _morphological_filtering(self, detection_mask):
        """形态学滤波去除噪声"""

        # 开运算去除小的噪声点
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        filtered_mask = cv2.morphologyEx(detection_mask.astype(np.uint8), cv2.MORPH_OPEN, kernel)

        # 闭运算连接相近的区域
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        filtered_mask = cv2.morphologyEx(filtered_mask, cv2.MORPH_CLOSE, kernel)

        return filtered_mask.astype(bool)

    def _create_processed_image(self, image_data, detection_mask, background_stats):
        """创建处理后的图像"""

        processed_image = image_data.copy()

        # 对检测到的区域进行增强
        processed_image[detection_mask] = processed_image[detection_mask] * 1.5

        # 对背景进行平滑
        background_mask = ~detection_mask
        if np.any(background_mask):
            # 使用高斯滤波平滑背景
            kernel = Gaussian2DKernel(x_stddev=1.0, y_stddev=1.0)
            smoothed_background = convolve(image_data, kernel, boundary='extend')
            processed_image[background_mask] = smoothed_background[background_mask]

        return processed_image

    def detect_sources(self, significance_map, detection_mask):
        """
        检测显著源

        Args:
            significance_map (np.ndarray): 显著性图像
            detection_mask (np.ndarray): 检测掩码

        Returns:
            list: 检测到的源列表
        """
        try:
            # 使用连通组件分析找到独立的源
            num_labels, labels = cv2.connectedComponents(detection_mask.astype(np.uint8))

            sources = []
            for label in range(1, num_labels):  # 跳过背景标签0
                source_mask = (labels == label)

                if np.sum(source_mask) < 5:  # 过滤太小的区域
                    continue

                # 计算源的属性
                y_coords, x_coords = np.where(source_mask)

                # 质心坐标
                total_significance = np.sum(significance_map[source_mask])
                if total_significance > 0:
                    centroid_x = np.sum(x_coords * significance_map[source_mask]) / total_significance
                    centroid_y = np.sum(y_coords * significance_map[source_mask]) / total_significance
                else:
                    centroid_x = np.mean(x_coords)
                    centroid_y = np.mean(y_coords)

                # 源的统计属性
                max_significance = np.max(significance_map[source_mask])
                mean_significance = np.mean(significance_map[source_mask])
                area = np.sum(source_mask)

                source = {
                    'id': label,
                    'x': float(centroid_x),
                    'y': float(centroid_y),
                    'max_significance': float(max_significance),
                    'mean_significance': float(mean_significance),
                    'total_significance': float(total_significance),
                    'area': int(area)
                }

                sources.append(source)

            # 按最大显著性排序
            sources.sort(key=lambda s: s['max_significance'], reverse=True)

            self.logger.info(f"检测到 {len(sources)} 个显著源")

            return sources

        except Exception as e:
            self.logger.error(f"源检测失败: {str(e)}")
            return []

    def save_fits_result(self, data, output_path, header=None):
        """
        保存FITS格式结果

        Args:
            data (np.ndarray): 图像数据
            output_path (str): 输出路径
            header (fits.Header): FITS头信息
        """
        try:
            if header is None:
                header = fits.Header()

            # 添加TheThresher处理信息
            header['HISTORY'] = f'Processed by DavidHoggThresher on {datetime.now().isoformat()}'
            header['THRESHER'] = 'DavidHoggThresher'
            header['THRVERS'] = '1.0'
            header['SIGTHRES'] = self.significance_threshold
            header['BAYESIAN'] = self.use_bayesian_inference

            hdu = fits.PrimaryHDU(data=data.astype(np.float32), header=header)
            hdu.writeto(output_path, overwrite=True)

            self.logger.info(f"FITS结果已保存: {output_path}")

        except Exception as e:
            self.logger.error(f"保存FITS文件失败 {output_path}: {str(e)}")

    def save_source_catalog(self, sources, output_path):
        """
        保存源目录

        Args:
            sources (list): 源列表
            output_path (str): 输出路径
        """
        try:
            with open(output_path, 'w') as f:
                # 写入头部
                f.write("# David Hogg TheThresher Source Catalog\n")
                f.write(f"# Generated on {datetime.now().isoformat()}\n")
                f.write(f"# Significance threshold: {self.significance_threshold}\n")
                f.write(f"# Bayesian inference: {self.use_bayesian_inference}\n")
                f.write("# Columns: ID X Y MAX_SIG MEAN_SIG TOTAL_SIG AREA\n")

                # 写入源数据
                for source in sources:
                    f.write(f"{source['id']:4d} {source['x']:8.3f} {source['y']:8.3f} "
                           f"{source['max_significance']:8.3f} {source['mean_significance']:8.3f} "
                           f"{source['total_significance']:12.3f} {source['area']:6d}\n")

            self.logger.info(f"源目录已保存: {output_path}")

        except Exception as e:
            self.logger.error(f"保存源目录失败 {output_path}: {str(e)}")

    def create_visualization(self, original_image, processed_image, significance_map,
                           detection_mask, sources, output_path):
        """
        创建可视化结果

        Args:
            original_image (np.ndarray): 原始图像
            processed_image (np.ndarray): 处理后图像
            significance_map (np.ndarray): 显著性图像
            detection_mask (np.ndarray): 检测掩码
            sources (list): 检测到的源
            output_path (str): 输出路径
        """
        try:
            fig, axes = plt.subplots(2, 2, figsize=(12, 12))
            fig.suptitle('David Hogg TheThresher Results', fontsize=16)

            # 原始图像
            vmin, vmax = np.percentile(original_image, [1, 99])
            im1 = axes[0, 0].imshow(original_image, cmap='gray', origin='lower', vmin=vmin, vmax=vmax)
            axes[0, 0].set_title('Original Image')
            axes[0, 0].set_xlabel('X (pixels)')
            axes[0, 0].set_ylabel('Y (pixels)')
            plt.colorbar(im1, ax=axes[0, 0])

            # 处理后图像
            vmin, vmax = np.percentile(processed_image, [1, 99])
            im2 = axes[0, 1].imshow(processed_image, cmap='gray', origin='lower', vmin=vmin, vmax=vmax)
            axes[0, 1].set_title('Thresher Processed Image')
            axes[0, 1].set_xlabel('X (pixels)')
            axes[0, 1].set_ylabel('Y (pixels)')
            plt.colorbar(im2, ax=axes[0, 1])

            # 显著性图像
            im3 = axes[1, 0].imshow(significance_map, cmap='RdBu_r', origin='lower')
            axes[1, 0].set_title('Significance Map')
            axes[1, 0].set_xlabel('X (pixels)')
            axes[1, 0].set_ylabel('Y (pixels)')
            plt.colorbar(im3, ax=axes[1, 0])

            # 检测结果
            im4 = axes[1, 1].imshow(original_image, cmap='gray', origin='lower', vmin=vmin, vmax=vmax)
            axes[1, 1].imshow(detection_mask, cmap='Reds', alpha=0.3, origin='lower')
            axes[1, 1].set_title(f'Detected Sources ({len(sources)})')
            axes[1, 1].set_xlabel('X (pixels)')
            axes[1, 1].set_ylabel('Y (pixels)')

            # 标记检测到的源
            for i, source in enumerate(sources[:20]):  # 只显示前20个最亮的源
                circle = plt.Circle((source['x'], source['y']), 10,
                                  fill=False, color='yellow', linewidth=2)
                axes[1, 1].add_patch(circle)
                axes[1, 1].text(source['x'] + 12, source['y'] + 12,
                               f"{i+1}", color='yellow', fontsize=8, fontweight='bold')

            plt.tight_layout()
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            plt.close()

            self.logger.info(f"可视化结果已保存: {output_path}")

        except Exception as e:
            self.logger.error(f"创建可视化失败 {output_path}: {str(e)}")

    def create_marked_fits(self, image_data, sources, output_path):
        """
        创建带有圆圈标记的FITS文件，圆圈大小根据AREA决定

        Args:
            image_data (np.ndarray): 原始图像数据
            sources (list): 检测到的源列表
            output_path (str): 输出路径
        """
        try:
            # 创建标记图像的副本
            marked_data = image_data.copy()

            # 获取图像尺寸
            height, width = image_data.shape

            if not sources:
                self.logger.warning("没有源需要标记")
                return

            # 计算AREA范围用于标准化圆圈大小
            areas = [s['area'] for s in sources]
            min_area = min(areas)
            max_area = max(areas)

            # 定义圆圈大小范围
            min_radius = 3   # 最小圆圈半径
            max_radius = 20  # 最大圆圈半径

            self.logger.info(f"标记 {len(sources)} 个源，面积范围: {min_area} - {max_area} 像素")

            # 为每个源绘制圆圈
            for i, source in enumerate(sources):
                x = int(round(source['x']))
                y = int(round(source['y']))
                area = source['area']
                max_significance = source['max_significance']

                # 根据AREA计算圆圈半径
                if max_area > min_area:
                    # 标准化面积到0-1范围
                    normalized_area = (area - min_area) / (max_area - min_area)
                else:
                    normalized_area = 0.5

                # 计算圆圈半径
                radius = int(min_radius + normalized_area * (max_radius - min_radius))

                # 根据显著性确定圆圈值
                # 正显著性用高值，负显著性用低值
                if max_significance > 0:
                    circle_value = np.max(image_data) * 1.2  # 比最大值高20%
                else:
                    circle_value = np.min(image_data) * 1.2  # 比最小值低20%

                # 绘制圆圈
                self._draw_circle(marked_data, x, y, radius, circle_value)

                # 在圆圈中心附近添加源ID标记
                if 0 <= x < width and 0 <= y < height:
                    # 在圆圈中心放置一个小点作为标记
                    marked_data[y, x] = circle_value * 0.8

            self.logger.info(f"完成源标记，圆圈半径范围: {min_radius} - {max_radius} 像素")

            # 保存标记后的FITS文件
            header = fits.Header()
            header['HISTORY'] = f'Marked by DavidHoggThresher on {datetime.now().isoformat()}'
            header['THRESHER'] = 'DavidHoggThresher'
            header['THRVERS'] = '1.0'
            header['MARKED'] = 'TRUE'
            header['NSOURCES'] = len(sources)
            header['MINAREA'] = min_area
            header['MAXAREA'] = max_area
            header['MINRAD'] = min_radius
            header['MAXRAD'] = max_radius
            header['COMMENT'] = 'Circle size proportional to source AREA'

            hdu = fits.PrimaryHDU(data=marked_data.astype(np.float32), header=header)
            hdu.writeto(output_path, overwrite=True)

            self.logger.info(f"标记FITS文件已保存: {output_path}")

        except Exception as e:
            self.logger.error(f"创建标记FITS文件失败 {output_path}: {str(e)}")

    def _draw_circle(self, image, center_x, center_y, radius, value):
        """
        在图像上绘制圆圈（优化版本）

        Args:
            image (np.ndarray): 图像数组
            center_x (int): 圆心X坐标
            center_y (int): 圆心Y坐标
            radius (int): 圆圈半径
            value (float): 圆圈像素值
        """
        height, width = image.shape

        # 边界检查
        if center_x < 0 or center_x >= width or center_y < 0 or center_y >= height:
            return

        # 只在圆圈周围的局部区域工作，提高效率
        margin = radius + 2
        x_min = max(0, center_x - margin)
        x_max = min(width, center_x + margin + 1)
        y_min = max(0, center_y - margin)
        y_max = min(height, center_y + margin + 1)

        # 创建局部坐标网格
        y_local, x_local = np.ogrid[y_min:y_max, x_min:x_max]

        # 计算到圆心的距离
        distances = np.sqrt((x_local - center_x)**2 + (y_local - center_y)**2)

        # 创建圆圈掩码（圆环，厚度为1像素）
        circle_mask = (distances >= radius - 0.5) & (distances <= radius + 0.5)

        # 应用圆圈到局部区域
        image[y_min:y_max, x_min:x_max][circle_mask] = value

    def process_difference_image(self, fits_path, output_dir=None):
        """
        处理差异图像的完整流程

        Args:
            fits_path (str): 差异图像FITS文件路径
            output_dir (str): 输出目录

        Returns:
            dict: 处理结果
        """
        try:
            self.logger.info("=" * 60)
            self.logger.info("开始David Hogg TheThresher处理")
            self.logger.info("=" * 60)

            # 设置输出目录
            if output_dir is None:
                output_dir = os.path.dirname(fits_path)
            os.makedirs(output_dir, exist_ok=True)

            # 1. 加载图像
            self.logger.info("步骤1: 加载差异图像")
            image_data, header = self.load_fits_image(fits_path)

            if image_data is None:
                self.logger.error("图像加载失败")
                return None

            # 2. 估计背景统计
            self.logger.info("步骤2: 估计背景统计特性")
            background_stats = self.estimate_background_statistics(image_data)

            if background_stats is None:
                self.logger.error("背景统计估计失败")
                return None

            # 3. 拟合统计模型
            self.logger.info("步骤3: 拟合TheThresher统计模型")
            model_params = self.fit_statistical_model(image_data, background_stats)

            if model_params is None:
                self.logger.error("统计模型拟合失败")
                return None

            # 4. 应用TheThresher算法
            self.logger.info("步骤4: 应用TheThresher算法")
            processed_image, significance_map, detection_mask = self.apply_thresher_algorithm(
                image_data, model_params, background_stats
            )

            if processed_image is None:
                self.logger.error("TheThresher算法应用失败")
                return None

            # 5. 检测源
            self.logger.info("步骤5: 检测显著源")
            sources = self.detect_sources(significance_map, detection_mask)

            # 6. 保存结果
            self.logger.info("步骤6: 保存结果")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_name = f"davidhogg_thresher_{timestamp}"

            # 保存处理后的图像
            processed_fits_path = os.path.join(output_dir, f"{base_name}_processed.fits")
            self.save_fits_result(processed_image, processed_fits_path, header)

            # 保存显著性图像
            significance_fits_path = os.path.join(output_dir, f"{base_name}_significance.fits")
            self.save_fits_result(significance_map, significance_fits_path, header)

            # 保存源目录
            catalog_path = os.path.join(output_dir, f"{base_name}_sources.txt")
            self.save_source_catalog(sources, catalog_path)

            # 创建带标记的FITS文件
            marked_fits_path = os.path.join(output_dir, f"{base_name}_marked.fits")
            self.create_marked_fits(image_data, sources, marked_fits_path)

            # 创建可视化
            viz_path = os.path.join(output_dir, f"{base_name}_visualization.png")
            self.create_visualization(image_data, processed_image, significance_map,
                                    detection_mask, sources, viz_path)

            # 返回结果
            result = {
                'success': True,
                'sources_detected': len(sources),
                'sources': sources,
                'output_directory': output_dir,
                'input_fits': fits_path,
                'processed_fits': processed_fits_path,
                'significance_fits': significance_fits_path,
                'marked_fits': marked_fits_path,
                'catalog_file': catalog_path,
                'visualization': viz_path,
                'background_stats': background_stats,
                'model_params': model_params,
                'processing_method': 'davidhogg_thresher'
            }

            self.logger.info(f"TheThresher处理完成，检测到 {len(sources)} 个显著源")
            return result

        except Exception as e:
            self.logger.error(f"TheThresher处理失败: {str(e)}")
            return None
