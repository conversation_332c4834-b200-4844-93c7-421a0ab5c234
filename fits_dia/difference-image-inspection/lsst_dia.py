#!/usr/bin/env python3
"""
LSSTDESC Difference-Image-Inspection Implementation
基于LSST DESC差异图像检查方法的天文图像处理实现

LSST (Legacy Survey of Space and Time) DESC (Dark Energy Science Collaboration)
的差异图像分析方法，专注于瞬变源检测、分类和质量评估。

核心特性：
1. 多尺度分析 - 不同空间尺度的源检测
2. 质量评估 - 图像质量和检测可靠性评估
3. 分类系统 - 瞬变源类型分类
4. 统计验证 - 基于LSST经验的统计验证

Reference: LSST Science Pipelines DIA algorithms
Author: Augment Agent
Date: 2025-07-23
"""

import os
import sys
import numpy as np
import logging
from datetime import datetime
from astropy.io import fits
from astropy.stats import sigma_clipped_stats, mad_std
from astropy.convolution import Gaussian2DKernel, convolve, Box2DKernel
from photutils import DAOStarFinder, aperture_photometry, CircularAperture
from photutils.segmentation import detect_sources, deblend_sources, SourceCatalog
from scipy import ndimage, optimize
from scipy.stats import chi2, norm
import matplotlib.pyplot as plt
from matplotlib.colors import LogNorm
import cv2
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler


class LSSTDifferenceImageInspection:
    """
    LSST DESC差异图像检查实现
    
    LSST DIA方法的核心思想：
    1. 多尺度检测 - 在不同空间尺度上检测源
    2. 质量评估 - 评估检测的可靠性和图像质量
    3. 源分类 - 区分不同类型的瞬变源
    4. 统计验证 - 使用LSST经验进行统计验证
    5. 假阳性过滤 - 基于形态学和统计特征过滤
    """
    
    def __init__(self, detection_threshold=5.0, quality_assessment=True):
        """
        初始化LSST DIA检查器
        
        Args:
            detection_threshold (float): 检测阈值
            quality_assessment (bool): 是否进行质量评估
        """
        self.detection_threshold = detection_threshold
        self.quality_assessment = quality_assessment
        self.setup_logging()
        
        # LSST DIA参数
        self.lsst_params = {
            'scales': [1.0, 2.0, 4.0, 8.0],     # 多尺度分析的尺度
            'min_area': 5,                       # 最小检测面积
            'deblend_nthresh': 32,              # 去混合阈值数
            'deblend_cont': 0.005,              # 去混合连续性
            'connectivity': 8,                   # 连通性
            'quality_flags': {                   # 质量标志
                'saturated': False,
                'interpolated': False,
                'cosmic_ray': False,
                'bad_pixel': False
            },
            'classification_features': [         # 分类特征
                'flux', 'magnitude', 'snr', 'fwhm', 
                'ellipticity', 'kron_radius', 'petrosian_radius'
            ]
        }
        
    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('lsst_dia.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def load_fits_image(self, fits_path):
        """
        加载FITS图像
        
        Args:
            fits_path (str): FITS文件路径
            
        Returns:
            tuple: (图像数据, 头信息)
        """
        try:
            with fits.open(fits_path) as hdul:
                data = hdul[0].data.astype(np.float64)
                header = hdul[0].header
                
            # 处理可能的3D数据
            if len(data.shape) == 3:
                data = data[0]
                
            self.logger.info(f"成功加载FITS文件: {fits_path}")
            self.logger.info(f"图像尺寸: {data.shape}")
            self.logger.info(f"数据范围: [{np.min(data):.6f}, {np.max(data):.6f}]")
            
            return data, header
            
        except Exception as e:
            self.logger.error(f"加载FITS文件失败 {fits_path}: {str(e)}")
            return None, None
            
    def assess_image_quality(self, image_data):
        """
        评估图像质量
        
        Args:
            image_data (np.ndarray): 图像数据
            
        Returns:
            dict: 图像质量评估结果
        """
        try:
            self.logger.info("评估图像质量...")
            
            # 基本统计
            mean, median, std = sigma_clipped_stats(image_data, sigma=3.0, maxiters=5)
            mad = mad_std(image_data)
            
            # 计算图像质量指标
            # 1. 信噪比
            snr = median / mad if mad > 0 else 0
            
            # 2. 动态范围
            dynamic_range = (np.max(image_data) - np.min(image_data)) / std if std > 0 else 0
            
            # 3. 对比度
            contrast = std / mean if mean > 0 else 0
            
            # 4. 检测饱和像素
            max_value = np.max(image_data)
            saturated_pixels = np.sum(image_data >= 0.95 * max_value)
            saturation_fraction = saturated_pixels / image_data.size
            
            # 5. 检测坏像素（异常值）
            z_scores = np.abs((image_data - median) / mad) if mad > 0 else np.zeros_like(image_data)
            bad_pixels = np.sum(z_scores > 5)
            bad_pixel_fraction = bad_pixels / image_data.size
            
            # 6. 计算图像熵（信息含量）
            hist, _ = np.histogram(image_data.flatten(), bins=256, density=True)
            hist = hist[hist > 0]  # 避免log(0)
            entropy = -np.sum(hist * np.log2(hist))
            
            quality_metrics = {
                'snr': snr,
                'dynamic_range': dynamic_range,
                'contrast': contrast,
                'saturation_fraction': saturation_fraction,
                'bad_pixel_fraction': bad_pixel_fraction,
                'entropy': entropy,
                'mean': mean,
                'median': median,
                'std': std,
                'mad': mad
            }
            
            # 综合质量评分 (0-100)
            quality_score = self._calculate_quality_score(quality_metrics)
            quality_metrics['overall_quality'] = quality_score
            
            self.logger.info(f"图像质量评估完成:")
            self.logger.info(f"  信噪比: {snr:.2f}")
            self.logger.info(f"  动态范围: {dynamic_range:.2f}")
            self.logger.info(f"  对比度: {contrast:.4f}")
            self.logger.info(f"  饱和像素比例: {saturation_fraction:.4f}")
            self.logger.info(f"  坏像素比例: {bad_pixel_fraction:.4f}")
            self.logger.info(f"  综合质量评分: {quality_score:.1f}/100")
            
            return quality_metrics
            
        except Exception as e:
            self.logger.error(f"图像质量评估失败: {str(e)}")
            return None
            
    def _calculate_quality_score(self, metrics):
        """计算综合质量评分"""
        score = 0
        
        # SNR贡献 (0-30分)
        snr_score = min(30, metrics['snr'] * 3)
        score += snr_score
        
        # 动态范围贡献 (0-25分)
        dr_score = min(25, metrics['dynamic_range'] / 10 * 25)
        score += dr_score
        
        # 对比度贡献 (0-20分)
        contrast_score = min(20, metrics['contrast'] * 200)
        score += contrast_score
        
        # 饱和像素惩罚 (0-15分)
        sat_penalty = max(0, 15 - metrics['saturation_fraction'] * 1500)
        score += sat_penalty
        
        # 坏像素惩罚 (0-10分)
        bad_penalty = max(0, 10 - metrics['bad_pixel_fraction'] * 1000)
        score += bad_penalty
        
        return min(100, score)
        
    def multiscale_detection(self, image_data, quality_metrics):
        """
        多尺度源检测
        
        Args:
            image_data (np.ndarray): 图像数据
            quality_metrics (dict): 图像质量指标
            
        Returns:
            list: 多尺度检测结果
        """
        try:
            self.logger.info("执行多尺度源检测...")
            
            all_sources = []
            
            for scale in self.lsst_params['scales']:
                self.logger.info(f"  尺度 {scale} 像素检测...")
                
                # 创建检测核
                if scale == 1.0:
                    # 最小尺度使用原始图像
                    detection_image = image_data.copy()
                else:
                    # 使用高斯卷积进行平滑
                    kernel = Gaussian2DKernel(x_stddev=scale, y_stddev=scale)
                    detection_image = convolve(image_data, kernel, boundary='extend')
                
                # 估计背景和噪声
                mean, median, std = sigma_clipped_stats(detection_image, sigma=3.0, maxiters=5)
                
                # 计算检测阈值
                threshold = median + self.detection_threshold * std
                
                # 源检测
                segm = detect_sources(detection_image, threshold, npixels=self.lsst_params['min_area'])
                
                if segm is None:
                    self.logger.info(f"    尺度 {scale}: 未检测到源")
                    continue
                
                # 去混合
                try:
                    segm_deblend = deblend_sources(detection_image, segm,
                                                 npixels=self.lsst_params['min_area'],
                                                 nthresh=self.lsst_params['deblend_nthresh'],
                                                 contrast=self.lsst_params['deblend_cont'])
                except TypeError:
                    # 处理不同版本的photutils API
                    try:
                        segm_deblend = deblend_sources(detection_image, segm,
                                                     npixels=self.lsst_params['min_area'],
                                                     n_thresholds=self.lsst_params['deblend_nthresh'],
                                                     contrast=self.lsst_params['deblend_cont'])
                    except:
                        # 如果去混合失败，使用原始分割
                        segm_deblend = segm
                
                # 创建源目录
                cat = SourceCatalog(detection_image, segm_deblend)
                
                # 提取源信息
                scale_sources = []
                for i, source in enumerate(cat):
                    try:
                        # 尝试不同的属性名称以兼容不同版本
                        flux = getattr(source, 'source_sum', getattr(source, 'segment_flux', 0))
                        area = getattr(source, 'area', getattr(source, 'segment_area', 0))

                        # 处理坐标
                        if hasattr(source, 'centroid'):
                            x, y = float(source.centroid[0]), float(source.centroid[1])
                        else:
                            x, y = float(getattr(source, 'xcentroid', 0)), float(getattr(source, 'ycentroid', 0))

                        # 处理形态学参数
                        try:
                            semi_major = float(source.semimajor_sigma.value)
                            semi_minor = float(source.semiminor_sigma.value)
                            ellipticity = float(source.ellipticity)
                            orientation = float(source.orientation.value)
                        except:
                            semi_major = semi_minor = 2.0
                            ellipticity = 0.0
                            orientation = 0.0

                        # 处理背景
                        background = getattr(source, 'local_background', 0)

                        source_info = {
                            'scale': scale,
                            'id': i + 1,
                            'x': x,
                            'y': y,
                            'flux': float(flux),
                            'area': int(area),
                            'semi_major': semi_major,
                            'semi_minor': semi_minor,
                            'ellipticity': ellipticity,
                            'orientation': orientation,
                            'background': float(background),
                            'snr': float(flux / np.sqrt(flux + area * std**2)) if flux > 0 and area > 0 else 0
                        }
                        scale_sources.append(source_info)

                    except Exception as e:
                        self.logger.warning(f"提取源 {i+1} 信息失败: {e}")
                        continue
                
                self.logger.info(f"    尺度 {scale}: 检测到 {len(scale_sources)} 个源")
                all_sources.extend(scale_sources)
            
            self.logger.info(f"多尺度检测完成，总计 {len(all_sources)} 个检测")
            
            return all_sources
            
        except Exception as e:
            self.logger.error(f"多尺度检测失败: {str(e)}")
            return []

    def classify_sources(self, sources, image_data):
        """
        源分类和质量评估

        Args:
            sources (list): 检测到的源列表
            image_data (np.ndarray): 图像数据

        Returns:
            list: 分类后的源列表
        """
        try:
            if not sources:
                return sources

            self.logger.info("执行源分类和质量评估...")

            classified_sources = []

            for source in sources:
                # 计算额外的分类特征
                enhanced_source = self._calculate_classification_features(source, image_data)

                # 执行分类
                source_class = self._classify_single_source(enhanced_source)
                enhanced_source['classification'] = source_class

                # 质量评估
                quality_flags = self._assess_source_quality(enhanced_source, image_data)
                enhanced_source['quality_flags'] = quality_flags

                # 计算可靠性评分
                reliability_score = self._calculate_reliability_score(enhanced_source)
                enhanced_source['reliability'] = reliability_score

                classified_sources.append(enhanced_source)

            # 按可靠性排序
            classified_sources.sort(key=lambda s: s['reliability'], reverse=True)

            # 统计分类结果
            class_counts = {}
            for source in classified_sources:
                class_name = source['classification']['class']
                class_counts[class_name] = class_counts.get(class_name, 0) + 1

            self.logger.info(f"源分类完成:")
            for class_name, count in class_counts.items():
                self.logger.info(f"  {class_name}: {count} 个")

            return classified_sources

        except Exception as e:
            self.logger.error(f"源分类失败: {str(e)}")
            return sources

    def _calculate_classification_features(self, source, image_data):
        """计算分类特征"""
        enhanced_source = source.copy()

        try:
            x, y = int(source['x']), int(source['y'])
            height, width = image_data.shape

            # 边界检查
            if x < 5 or x >= width - 5 or y < 5 or y >= height - 5:
                enhanced_source.update({
                    'magnitude': 99.0,
                    'fwhm': 0.0,
                    'kron_radius': 0.0,
                    'petrosian_radius': 0.0,
                    'concentration': 0.0,
                    'asymmetry': 0.0
                })
                return enhanced_source

            # 计算星等
            if source['flux'] > 0:
                magnitude = -2.5 * np.log10(source['flux']) + 25.0  # 假设零点为25
            else:
                magnitude = 99.0

            # 估计FWHM
            fwhm = 2.355 * np.sqrt(source['semi_major'] * source['semi_minor'])

            # Kron半径估计
            kron_radius = np.sqrt(source['area'] / np.pi)

            # Petrosian半径估计
            petrosian_radius = kron_radius * 1.5

            # 浓度指数 (中心亮度 vs 总亮度)
            center_flux = image_data[y, x]
            concentration = center_flux / source['flux'] if source['flux'] > 0 else 0

            # 不对称性
            cutout_size = min(20, int(kron_radius * 3))
            if cutout_size > 2:
                y1, y2 = max(0, y - cutout_size), min(height, y + cutout_size + 1)
                x1, x2 = max(0, x - cutout_size), min(width, x + cutout_size + 1)
                cutout = image_data[y1:y2, x1:x2]

                # 计算180度旋转后的差异
                rotated = np.rot90(cutout, 2)
                if cutout.shape == rotated.shape:
                    asymmetry = np.sum(np.abs(cutout - rotated)) / np.sum(np.abs(cutout))
                else:
                    asymmetry = 0.0
            else:
                asymmetry = 0.0

            enhanced_source.update({
                'magnitude': magnitude,
                'fwhm': fwhm,
                'kron_radius': kron_radius,
                'petrosian_radius': petrosian_radius,
                'concentration': concentration,
                'asymmetry': asymmetry
            })

        except Exception as e:
            self.logger.warning(f"计算分类特征失败: {e}")

        return enhanced_source

    def _classify_single_source(self, source):
        """单个源分类"""

        # 基于LSST经验的简化分类规则
        classification = {
            'class': 'unknown',
            'confidence': 0.0,
            'subclass': None
        }

        try:
            # 特征提取
            snr = source.get('snr', 0)
            fwhm = source.get('fwhm', 0)
            ellipticity = source.get('ellipticity', 0)
            concentration = source.get('concentration', 0)
            asymmetry = source.get('asymmetry', 0)
            area = source.get('area', 0)

            # 分类逻辑
            if snr < 3:
                classification = {'class': 'noise', 'confidence': 0.9, 'subclass': 'low_snr'}
            elif area < 5:
                classification = {'class': 'artifact', 'confidence': 0.8, 'subclass': 'small_area'}
            elif fwhm < 1.5:
                classification = {'class': 'cosmic_ray', 'confidence': 0.7, 'subclass': 'sharp'}
            elif ellipticity > 0.8:
                classification = {'class': 'artifact', 'confidence': 0.6, 'subclass': 'elongated'}
            elif concentration > 0.8 and fwhm < 4.0:
                classification = {'class': 'star', 'confidence': 0.8, 'subclass': 'point_source'}
            elif concentration < 0.3 and fwhm > 6.0:
                classification = {'class': 'galaxy', 'confidence': 0.7, 'subclass': 'extended'}
            elif asymmetry > 0.5:
                classification = {'class': 'transient', 'confidence': 0.6, 'subclass': 'asymmetric'}
            elif snr > 10 and 2.0 < fwhm < 8.0:
                classification = {'class': 'transient', 'confidence': 0.8, 'subclass': 'candidate'}
            else:
                classification = {'class': 'unknown', 'confidence': 0.3, 'subclass': 'unclassified'}

        except Exception as e:
            self.logger.warning(f"源分类失败: {e}")

        return classification

    def _assess_source_quality(self, source, image_data):
        """评估源质量"""
        quality_flags = {
            'saturated': False,
            'interpolated': False,
            'cosmic_ray': False,
            'bad_pixel': False,
            'edge': False,
            'blended': False
        }

        try:
            x, y = int(source['x']), int(source['y'])
            height, width = image_data.shape

            # 边缘检测
            if x < 10 or x >= width - 10 or y < 10 or y >= height - 10:
                quality_flags['edge'] = True

            # 饱和检测
            max_value = np.max(image_data)
            if image_data[y, x] > 0.9 * max_value:
                quality_flags['saturated'] = True

            # 宇宙射线检测
            if source.get('fwhm', 0) < 1.5 and source.get('snr', 0) > 20:
                quality_flags['cosmic_ray'] = True

            # 混合源检测
            if source.get('area', 0) > 100 and source.get('ellipticity', 0) > 0.6:
                quality_flags['blended'] = True

        except Exception as e:
            self.logger.warning(f"质量评估失败: {e}")

        return quality_flags

    def _calculate_reliability_score(self, source):
        """计算可靠性评分"""
        score = 50.0  # 基础分数

        try:
            # SNR贡献
            snr = source.get('snr', 0)
            score += min(30, snr * 2)

            # 分类置信度贡献
            confidence = source.get('classification', {}).get('confidence', 0)
            score += confidence * 20

            # 质量标志惩罚
            quality_flags = source.get('quality_flags', {})
            for flag, value in quality_flags.items():
                if value:
                    if flag in ['saturated', 'cosmic_ray']:
                        score -= 20
                    elif flag in ['edge', 'blended']:
                        score -= 10
                    else:
                        score -= 5

            # 形态学特征
            fwhm = source.get('fwhm', 0)
            if 2.0 <= fwhm <= 8.0:
                score += 10

            ellipticity = source.get('ellipticity', 0)
            if ellipticity < 0.5:
                score += 5

        except Exception as e:
            self.logger.warning(f"可靠性评分计算失败: {e}")

        return max(0, min(100, score))

    def cluster_analysis(self, sources):
        """
        聚类分析 - 识别相关的源群

        Args:
            sources (list): 源列表

        Returns:
            list: 带有聚类信息的源列表
        """
        try:
            if len(sources) < 2:
                for source in sources:
                    source['cluster_id'] = 0
                return sources

            self.logger.info("执行聚类分析...")

            # 准备聚类特征
            features = []
            for source in sources:
                feature_vector = [
                    source['x'], source['y'],
                    source.get('magnitude', 25),
                    source.get('fwhm', 3),
                    source.get('ellipticity', 0.5),
                    source['scale']
                ]
                features.append(feature_vector)

            features = np.array(features)

            # 标准化特征
            scaler = StandardScaler()
            features_scaled = scaler.fit_transform(features)

            # DBSCAN聚类
            clustering = DBSCAN(eps=0.5, min_samples=2)
            cluster_labels = clustering.fit_predict(features_scaled)

            # 添加聚类信息到源
            for i, source in enumerate(sources):
                source['cluster_id'] = int(cluster_labels[i])

            # 统计聚类结果
            unique_clusters = set(cluster_labels)
            n_clusters = len(unique_clusters) - (1 if -1 in cluster_labels else 0)
            n_noise = list(cluster_labels).count(-1)

            self.logger.info(f"聚类分析完成:")
            self.logger.info(f"  聚类数: {n_clusters}")
            self.logger.info(f"  噪声点: {n_noise}")

            return sources

        except Exception as e:
            self.logger.error(f"聚类分析失败: {str(e)}")
            # 如果聚类失败，给所有源分配cluster_id = 0
            for source in sources:
                source['cluster_id'] = 0
            return sources

    def statistical_validation(self, sources, image_data):
        """
        统计验证 - 基于LSST经验的统计验证

        Args:
            sources (list): 源列表
            image_data (np.ndarray): 图像数据

        Returns:
            dict: 统计验证结果
        """
        try:
            self.logger.info("执行统计验证...")

            if not sources:
                return {'validation_passed': False, 'reason': 'no_sources'}

            # 计算统计量
            snr_values = [s.get('snr', 0) for s in sources]
            magnitude_values = [s.get('magnitude', 25) for s in sources if s.get('magnitude', 25) < 30]
            area_values = [s.get('area', 0) for s in sources]

            # 基本统计
            stats = {
                'n_sources': len(sources),
                'mean_snr': np.mean(snr_values) if snr_values else 0,
                'median_snr': np.median(snr_values) if snr_values else 0,
                'mean_magnitude': np.mean(magnitude_values) if magnitude_values else 25,
                'median_magnitude': np.median(magnitude_values) if magnitude_values else 25,
                'mean_area': np.mean(area_values) if area_values else 0
            }

            # 验证规则（基于LSST经验）
            validation_results = {
                'validation_passed': True,
                'warnings': [],
                'statistics': stats
            }

            # 检查源密度
            image_area = image_data.size
            source_density = len(sources) / image_area * 1e6  # 每百万像素的源数

            if source_density > 1000:  # 过高的源密度可能表示噪声
                validation_results['warnings'].append('high_source_density')

            if source_density < 0.1:  # 过低的源密度可能表示检测问题
                validation_results['warnings'].append('low_source_density')

            # 检查SNR分布
            high_snr_sources = len([s for s in sources if s.get('snr', 0) > 10])
            if high_snr_sources / len(sources) < 0.1:
                validation_results['warnings'].append('low_high_snr_fraction')

            # 检查分类分布
            class_counts = {}
            for source in sources:
                class_name = source.get('classification', {}).get('class', 'unknown')
                class_counts[class_name] = class_counts.get(class_name, 0) + 1

            noise_fraction = class_counts.get('noise', 0) / len(sources)
            if noise_fraction > 0.5:
                validation_results['warnings'].append('high_noise_fraction')

            validation_results['class_distribution'] = class_counts
            validation_results['source_density'] = source_density

            self.logger.info(f"统计验证完成:")
            self.logger.info(f"  源密度: {source_density:.1f} 个/百万像素")
            self.logger.info(f"  平均SNR: {stats['mean_snr']:.2f}")
            self.logger.info(f"  警告数: {len(validation_results['warnings'])}")

            return validation_results

        except Exception as e:
            self.logger.error(f"统计验证失败: {str(e)}")
            return {'validation_passed': False, 'reason': 'validation_error'}

    def save_fits_result(self, data, output_path, header=None):
        """
        保存FITS格式结果

        Args:
            data (np.ndarray): 图像数据
            output_path (str): 输出路径
            header (fits.Header): FITS头信息
        """
        try:
            if header is None:
                header = fits.Header()

            # 添加LSST DIA处理信息
            header['HISTORY'] = f'Processed by LSST DIA on {datetime.now().isoformat()}'
            header['LSSTDIA'] = 'LSSTDifferenceImageInspection'
            header['DIAVERS'] = '1.0'
            header['DETTHRES'] = self.detection_threshold
            header['QUALEVAL'] = self.quality_assessment

            hdu = fits.PrimaryHDU(data=data.astype(np.float32), header=header)
            hdu.writeto(output_path, overwrite=True)

            self.logger.info(f"FITS结果已保存: {output_path}")

        except Exception as e:
            self.logger.error(f"保存FITS文件失败 {output_path}: {str(e)}")

    def save_source_catalog(self, sources, output_path, validation_results=None):
        """
        保存源目录

        Args:
            sources (list): 源列表
            output_path (str): 输出路径
            validation_results (dict): 统计验证结果
        """
        try:
            with open(output_path, 'w') as f:
                # 写入头部
                f.write("# LSST DESC Difference Image Inspection Source Catalog\n")
                f.write(f"# Generated on {datetime.now().isoformat()}\n")
                f.write(f"# Detection threshold: {self.detection_threshold}\n")
                f.write(f"# Quality assessment: {self.quality_assessment}\n")

                if validation_results:
                    f.write(f"# Source density: {validation_results.get('source_density', 0):.1f} per Mpixel\n")
                    f.write(f"# Validation warnings: {len(validation_results.get('warnings', []))}\n")

                f.write("# Columns: ID SCALE X Y FLUX AREA SNR MAG FWHM ELLIP CLASS CONF RELIABILITY CLUSTER\n")

                # 写入源数据
                for i, source in enumerate(sources):
                    classification = source.get('classification', {})
                    f.write(f"{i+1:4d} {source['scale']:4.1f} {source['x']:8.3f} {source['y']:8.3f} "
                           f"{source['flux']:12.6e} {source['area']:6d} {source.get('snr', 0):6.2f} "
                           f"{source.get('magnitude', 99):6.2f} {source.get('fwhm', 0):6.2f} "
                           f"{source.get('ellipticity', 0):6.3f} {classification.get('class', 'unknown'):12s} "
                           f"{classification.get('confidence', 0):5.2f} {source.get('reliability', 0):6.1f} "
                           f"{source.get('cluster_id', -1):4d}\n")

            self.logger.info(f"源目录已保存: {output_path}")

        except Exception as e:
            self.logger.error(f"保存源目录失败 {output_path}: {str(e)}")

    def create_visualization(self, image_data, sources, quality_metrics,
                           validation_results, output_path):
        """
        创建LSST DIA可视化结果

        Args:
            image_data (np.ndarray): 原始图像
            sources (list): 检测到的源
            quality_metrics (dict): 图像质量指标
            validation_results (dict): 统计验证结果
            output_path (str): 输出路径
        """
        try:
            fig = plt.figure(figsize=(16, 12))

            # 创建网格布局
            gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)

            # 1. 原始图像
            ax1 = fig.add_subplot(gs[0, 0])
            vmin, vmax = np.percentile(image_data, [1, 99])
            im1 = ax1.imshow(image_data, cmap='gray', origin='lower', vmin=vmin, vmax=vmax)
            ax1.set_title('Original Difference Image')
            ax1.set_xlabel('X (pixels)')
            ax1.set_ylabel('Y (pixels)')
            plt.colorbar(im1, ax=ax1, shrink=0.8)

            # 2. 检测结果
            ax2 = fig.add_subplot(gs[0, 1])
            ax2.imshow(image_data, cmap='gray', origin='lower', vmin=vmin, vmax=vmax)

            # 按分类着色标记源
            class_colors = {
                'transient': 'red', 'star': 'blue', 'galaxy': 'green',
                'cosmic_ray': 'orange', 'artifact': 'purple', 'noise': 'gray',
                'unknown': 'yellow'
            }

            for source in sources[:50]:  # 只显示前50个最可靠的源
                class_name = source.get('classification', {}).get('class', 'unknown')
                color = class_colors.get(class_name, 'white')

                circle = plt.Circle((source['x'], source['y']),
                                  source.get('fwhm', 3),
                                  fill=False, color=color, linewidth=1.5)
                ax2.add_patch(circle)

            ax2.set_title(f'Detected Sources ({len(sources)})')
            ax2.set_xlabel('X (pixels)')
            ax2.set_ylabel('Y (pixels)')

            # 3. 多尺度检测分布
            ax3 = fig.add_subplot(gs[0, 2])
            scales = [s['scale'] for s in sources]
            ax3.hist(scales, bins=len(self.lsst_params['scales']), alpha=0.7, edgecolor='black')
            ax3.set_title('Multi-scale Detection')
            ax3.set_xlabel('Detection Scale')
            ax3.set_ylabel('Number of Sources')
            ax3.grid(True, alpha=0.3)

            # 4. SNR分布
            ax4 = fig.add_subplot(gs[1, 0])
            snr_values = [s.get('snr', 0) for s in sources if s.get('snr', 0) > 0]
            if snr_values:
                ax4.hist(snr_values, bins=30, alpha=0.7, edgecolor='black')
                ax4.axvline(self.detection_threshold, color='red', linestyle='--',
                           label=f'Threshold ({self.detection_threshold})')
                ax4.legend()
            ax4.set_title('SNR Distribution')
            ax4.set_xlabel('Signal-to-Noise Ratio')
            ax4.set_ylabel('Number of Sources')
            ax4.grid(True, alpha=0.3)

            # 5. 分类饼图
            ax5 = fig.add_subplot(gs[1, 1])
            class_counts = validation_results.get('class_distribution', {})
            if class_counts:
                labels = list(class_counts.keys())
                sizes = list(class_counts.values())
                colors = [class_colors.get(label, 'gray') for label in labels]
                ax5.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            ax5.set_title('Source Classification')

            # 6. 可靠性分布
            ax6 = fig.add_subplot(gs[1, 2])
            reliability_values = [s.get('reliability', 0) for s in sources]
            if reliability_values:
                ax6.hist(reliability_values, bins=20, alpha=0.7, edgecolor='black')
                ax6.axvline(50, color='red', linestyle='--', label='Threshold (50)')
                ax6.legend()
            ax6.set_title('Reliability Distribution')
            ax6.set_xlabel('Reliability Score')
            ax6.set_ylabel('Number of Sources')
            ax6.grid(True, alpha=0.3)

            # 7. 图像质量指标
            ax7 = fig.add_subplot(gs[2, 0])
            quality_labels = ['SNR', 'Dynamic\nRange', 'Contrast', 'Overall\nQuality']
            quality_values = [
                quality_metrics.get('snr', 0),
                quality_metrics.get('dynamic_range', 0) / 10,  # 缩放
                quality_metrics.get('contrast', 0) * 100,      # 缩放
                quality_metrics.get('overall_quality', 0)
            ]
            bars = ax7.bar(quality_labels, quality_values, alpha=0.7)
            ax7.set_title('Image Quality Metrics')
            ax7.set_ylabel('Score')
            ax7.grid(True, alpha=0.3)

            # 为每个柱子添加数值标签
            for bar, value in zip(bars, quality_values):
                height = bar.get_height()
                ax7.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                        f'{value:.1f}', ha='center', va='bottom')

            # 8. 聚类结果
            ax8 = fig.add_subplot(gs[2, 1])
            if sources:
                x_coords = [s['x'] for s in sources]
                y_coords = [s['y'] for s in sources]
                cluster_ids = [s.get('cluster_id', 0) for s in sources]

                scatter = ax8.scatter(x_coords, y_coords, c=cluster_ids,
                                    cmap='tab10', alpha=0.7, s=20)
                ax8.set_title('Spatial Clustering')
                ax8.set_xlabel('X (pixels)')
                ax8.set_ylabel('Y (pixels)')
                plt.colorbar(scatter, ax=ax8, shrink=0.8)

            # 9. 统计摘要
            ax9 = fig.add_subplot(gs[2, 2])
            ax9.axis('off')

            # 创建统计摘要文本
            summary_text = f"""LSST DIA Summary

Sources Detected: {len(sources)}
Quality Score: {quality_metrics.get('overall_quality', 0):.1f}/100
Source Density: {validation_results.get('source_density', 0):.1f}/Mpx

High Reliability: {len([s for s in sources if s.get('reliability', 0) > 70])}
Medium Reliability: {len([s for s in sources if 30 <= s.get('reliability', 0) <= 70])}
Low Reliability: {len([s for s in sources if s.get('reliability', 0) < 30])}

Warnings: {len(validation_results.get('warnings', []))}
Validation: {'PASS' if validation_results.get('validation_passed', False) else 'FAIL'}
            """

            ax9.text(0.05, 0.95, summary_text, transform=ax9.transAxes,
                    fontsize=10, verticalalignment='top', fontfamily='monospace',
                    bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))

            plt.suptitle('LSST DESC Difference Image Inspection Results', fontsize=16)
            plt.tight_layout()
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            plt.close()

            self.logger.info(f"可视化结果已保存: {output_path}")

        except Exception as e:
            self.logger.error(f"创建可视化失败 {output_path}: {str(e)}")

    def create_marked_fits(self, image_data, sources, output_path):
        """
        创建带有圆圈标记的FITS文件，圆圈大小根据flux和SNR决定

        Args:
            image_data (np.ndarray): 原始图像数据
            sources (list): 检测到的源列表
            output_path (str): 输出路径
        """
        try:
            # 创建标记图像的副本
            marked_data = image_data.copy()

            # 获取图像尺寸
            height, width = image_data.shape

            if not sources:
                self.logger.warning("没有源需要标记")
                return

            # 计算flux和SNR范围用于标准化圆圈大小
            fluxes = [abs(s['flux']) for s in sources if s.get('flux', 0) != 0]
            snrs = [s.get('snr', 0) for s in sources if s.get('snr', 0) > 0]

            if not fluxes or not snrs:
                self.logger.warning("源数据中缺少有效的flux或SNR信息")
                return

            min_flux, max_flux = min(fluxes), max(fluxes)
            min_snr, max_snr = min(snrs), max(snrs)

            # 定义圆圈大小范围
            min_radius = 3   # 最小圆圈半径
            max_radius = 25  # 最大圆圈半径

            self.logger.info(f"标记 {len(sources)} 个源")
            self.logger.info(f"Flux范围: {min_flux:.6e} - {max_flux:.6e}")
            self.logger.info(f"SNR范围: {min_snr:.2f} - {max_snr:.2f}")

            # 为每个源绘制圆圈
            for i, source in enumerate(sources):
                x = int(round(source['x']))
                y = int(round(source['y']))
                flux = source.get('flux', 0)
                snr = source.get('snr', 0)

                # 根据flux和SNR计算圆圈半径
                # 使用flux和SNR的加权组合来确定圆圈大小
                if max_flux > min_flux and max_snr > min_snr:
                    # 标准化flux和SNR到0-1范围
                    normalized_flux = (abs(flux) - min_flux) / (max_flux - min_flux)
                    normalized_snr = (snr - min_snr) / (max_snr - min_snr)

                    # 使用flux和SNR的加权平均 (flux权重0.6, SNR权重0.4)
                    combined_score = 0.6 * normalized_flux + 0.4 * normalized_snr
                else:
                    combined_score = 0.5

                # 计算圆圈半径
                radius = int(min_radius + combined_score * (max_radius - min_radius))

                # 根据flux正负和可靠性确定圆圈值
                reliability = source.get('reliability', 50)
                classification = source.get('classification', {})
                class_name = classification.get('class', 'unknown')

                # 根据分类和可靠性确定圆圈亮度
                if class_name in ['transient', 'star'] and reliability > 70:
                    # 高可靠性的瞬变源和恒星用最亮圆圈
                    circle_value = np.max(image_data) * 1.5
                elif flux > 0:
                    # 正流量源用高亮圆圈
                    circle_value = np.max(image_data) * 1.2
                else:
                    # 负流量源用暗色圆圈
                    circle_value = np.min(image_data) * 1.2

                # 绘制圆圈
                self._draw_circle(marked_data, x, y, radius, circle_value)

                # 在圆圈中心附近添加源标记
                if 0 <= x < width and 0 <= y < height:
                    # 在圆圈中心放置一个小点作为标记
                    marked_data[y, x] = circle_value * 0.8

            self.logger.info(f"完成源标记，圆圈半径范围: {min_radius} - {max_radius} 像素")

            # 保存标记后的FITS文件
            header = fits.Header()
            header['HISTORY'] = f'Marked by LSST DIA on {datetime.now().isoformat()}'
            header['LSSTDIA'] = 'LSSTDifferenceImageInspection'
            header['DIAVERS'] = '1.0'
            header['MARKED'] = 'TRUE'
            header['NSOURCES'] = len(sources)
            header['MINFLUX'] = min_flux
            header['MAXFLUX'] = max_flux
            header['MINSNR'] = min_snr
            header['MAXSNR'] = max_snr
            header['MINRAD'] = min_radius
            header['MAXRAD'] = max_radius
            header['COMMENT'] = 'Circle size based on flux (60%) and SNR (40%)'

            hdu = fits.PrimaryHDU(data=marked_data.astype(np.float32), header=header)
            hdu.writeto(output_path, overwrite=True)

            self.logger.info(f"标记FITS文件已保存: {output_path}")

        except Exception as e:
            self.logger.error(f"创建标记FITS文件失败 {output_path}: {str(e)}")

    def _draw_circle(self, image, center_x, center_y, radius, value):
        """
        在图像上绘制圆圈（优化版本）

        Args:
            image (np.ndarray): 图像数组
            center_x (int): 圆心X坐标
            center_y (int): 圆心Y坐标
            radius (int): 圆圈半径
            value (float): 圆圈像素值
        """
        height, width = image.shape

        # 边界检查
        if center_x < 0 or center_x >= width or center_y < 0 or center_y >= height:
            return

        # 只在圆圈周围的局部区域工作，提高效率
        margin = radius + 2
        x_min = max(0, center_x - margin)
        x_max = min(width, center_x + margin + 1)
        y_min = max(0, center_y - margin)
        y_max = min(height, center_y + margin + 1)

        # 创建局部坐标网格
        y_local, x_local = np.ogrid[y_min:y_max, x_min:x_max]

        # 计算到圆心的距离
        distances = np.sqrt((x_local - center_x)**2 + (y_local - center_y)**2)

        # 创建圆圈掩码（圆环，厚度为1-2像素）
        circle_mask = (distances >= radius - 1) & (distances <= radius + 1)

        # 应用圆圈到局部区域
        image[y_min:y_max, x_min:x_max][circle_mask] = value

    def process_difference_image(self, fits_path, output_dir=None):
        """
        处理差异图像的完整LSST DIA流程

        Args:
            fits_path (str): 差异图像FITS文件路径
            output_dir (str): 输出目录

        Returns:
            dict: 处理结果
        """
        try:
            self.logger.info("=" * 60)
            self.logger.info("开始LSST DESC差异图像检查")
            self.logger.info("=" * 60)

            # 设置输出目录
            if output_dir is None:
                output_dir = os.path.dirname(fits_path)
            os.makedirs(output_dir, exist_ok=True)

            # 1. 加载图像
            self.logger.info("步骤1: 加载差异图像")
            image_data, header = self.load_fits_image(fits_path)

            if image_data is None:
                self.logger.error("图像加载失败")
                return None

            # 2. 图像质量评估
            self.logger.info("步骤2: 评估图像质量")
            quality_metrics = self.assess_image_quality(image_data)

            if quality_metrics is None:
                self.logger.error("图像质量评估失败")
                return None

            # 3. 多尺度源检测
            self.logger.info("步骤3: 多尺度源检测")
            sources = self.multiscale_detection(image_data, quality_metrics)

            if not sources:
                self.logger.warning("未检测到任何源")

            # 4. 源分类和质量评估
            self.logger.info("步骤4: 源分类和质量评估")
            classified_sources = self.classify_sources(sources, image_data)

            # 5. 聚类分析
            self.logger.info("步骤5: 聚类分析")
            clustered_sources = self.cluster_analysis(classified_sources)

            # 6. 统计验证
            self.logger.info("步骤6: 统计验证")
            validation_results = self.statistical_validation(clustered_sources, image_data)

            # 7. 保存结果
            self.logger.info("步骤7: 保存结果")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_name = f"lsst_dia_{timestamp}"

            # 保存源目录
            catalog_path = os.path.join(output_dir, f"{base_name}_sources.txt")
            self.save_source_catalog(clustered_sources, catalog_path, validation_results)

            # 创建带标记的FITS文件
            marked_fits_path = os.path.join(output_dir, f"{base_name}_marked.fits")
            self.create_marked_fits(image_data, clustered_sources, marked_fits_path)

            # 创建可视化
            viz_path = os.path.join(output_dir, f"{base_name}_visualization.png")
            self.create_visualization(image_data, clustered_sources, quality_metrics,
                                    validation_results, viz_path)

            # 保存质量评估报告
            report_path = os.path.join(output_dir, f"{base_name}_quality_report.txt")
            self.save_quality_report(quality_metrics, validation_results, report_path)

            # 返回结果
            result = {
                'success': True,
                'sources_detected': len(clustered_sources),
                'sources': clustered_sources,
                'output_directory': output_dir,
                'input_fits': fits_path,
                'marked_fits': marked_fits_path,
                'catalog_file': catalog_path,
                'visualization': viz_path,
                'quality_report': report_path,
                'quality_metrics': quality_metrics,
                'validation_results': validation_results,
                'processing_method': 'lsst_desc_dia'
            }

            self.logger.info(f"LSST DIA处理完成，检测到 {len(clustered_sources)} 个源")
            self.logger.info(f"图像质量评分: {quality_metrics.get('overall_quality', 0):.1f}/100")
            self.logger.info(f"统计验证: {'通过' if validation_results.get('validation_passed', False) else '失败'}")

            return result

        except Exception as e:
            self.logger.error(f"LSST DIA处理失败: {str(e)}")
            return None

    def save_quality_report(self, quality_metrics, validation_results, output_path):
        """
        保存质量评估报告

        Args:
            quality_metrics (dict): 图像质量指标
            validation_results (dict): 统计验证结果
            output_path (str): 输出路径
        """
        try:
            with open(output_path, 'w') as f:
                f.write("# LSST DESC Difference Image Inspection Quality Report\n")
                f.write(f"# Generated on {datetime.now().isoformat()}\n")
                f.write("=" * 60 + "\n\n")

                # 图像质量指标
                f.write("IMAGE QUALITY METRICS\n")
                f.write("-" * 30 + "\n")
                f.write(f"Overall Quality Score: {quality_metrics.get('overall_quality', 0):.1f}/100\n")
                f.write(f"Signal-to-Noise Ratio: {quality_metrics.get('snr', 0):.2f}\n")
                f.write(f"Dynamic Range: {quality_metrics.get('dynamic_range', 0):.2f}\n")
                f.write(f"Contrast: {quality_metrics.get('contrast', 0):.4f}\n")
                f.write(f"Saturation Fraction: {quality_metrics.get('saturation_fraction', 0):.4f}\n")
                f.write(f"Bad Pixel Fraction: {quality_metrics.get('bad_pixel_fraction', 0):.4f}\n")
                f.write(f"Image Entropy: {quality_metrics.get('entropy', 0):.2f}\n\n")

                # 统计验证结果
                f.write("STATISTICAL VALIDATION\n")
                f.write("-" * 30 + "\n")
                f.write(f"Validation Status: {'PASSED' if validation_results.get('validation_passed', False) else 'FAILED'}\n")
                f.write(f"Number of Sources: {validation_results.get('statistics', {}).get('n_sources', 0)}\n")
                f.write(f"Source Density: {validation_results.get('source_density', 0):.1f} per Mpixel\n")
                f.write(f"Mean SNR: {validation_results.get('statistics', {}).get('mean_snr', 0):.2f}\n")
                f.write(f"Mean Magnitude: {validation_results.get('statistics', {}).get('mean_magnitude', 0):.2f}\n\n")

                # 分类分布
                f.write("SOURCE CLASSIFICATION DISTRIBUTION\n")
                f.write("-" * 30 + "\n")
                class_dist = validation_results.get('class_distribution', {})
                for class_name, count in class_dist.items():
                    percentage = count / validation_results.get('statistics', {}).get('n_sources', 1) * 100
                    f.write(f"{class_name:12s}: {count:4d} ({percentage:5.1f}%)\n")
                f.write("\n")

                # 警告信息
                warnings = validation_results.get('warnings', [])
                if warnings:
                    f.write("VALIDATION WARNINGS\n")
                    f.write("-" * 30 + "\n")
                    for warning in warnings:
                        f.write(f"- {warning}\n")
                    f.write("\n")

                # 处理参数
                f.write("PROCESSING PARAMETERS\n")
                f.write("-" * 30 + "\n")
                f.write(f"Detection Threshold: {self.detection_threshold} sigma\n")
                f.write(f"Quality Assessment: {self.quality_assessment}\n")
                f.write(f"Detection Scales: {self.lsst_params['scales']}\n")
                f.write(f"Minimum Area: {self.lsst_params['min_area']} pixels\n")

            self.logger.info(f"质量报告已保存: {output_path}")

        except Exception as e:
            self.logger.error(f"保存质量报告失败 {output_path}: {str(e)}")
