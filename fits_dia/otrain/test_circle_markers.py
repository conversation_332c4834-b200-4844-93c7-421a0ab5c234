#!/usr/bin/env python3
"""
测试圆圈标记效果的脚本
显示带有新圆圈标记的FITS文件
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from astropy.io import fits
from matplotlib.colors import LogNorm

def display_marked_fits(fits_path, output_path=None):
    """
    显示带有圆圈标记的FITS文件
    
    Args:
        fits_path (str): 标记的FITS文件路径
        output_path (str): 输出图像路径（可选）
    """
    try:
        # 读取FITS文件
        with fits.open(fits_path) as hdul:
            image_data = hdul[0].data
            header = hdul[0].header
        
        print(f"图像尺寸: {image_data.shape}")
        print(f"数据范围: [{np.min(image_data):.6f}, {np.max(image_data):.6f}]")
        
        # 获取OTRAIN信息
        if 'OTCANDS' in header:
            print(f"候选天体数量: {header['OTCANDS']}")
        if 'OTREALS' in header:
            print(f"真实瞬变天体数量: {header['OTREALS']}")
        
        # 创建显示图像
        fig, axes = plt.subplots(1, 2, figsize=(20, 10))
        
        # 左图：原始标记图像
        im1 = axes[0].imshow(image_data, cmap='gray', origin='lower')
        axes[0].set_title('带圆圈标记的差异图像\n(Real: 实心圆圈, Bogus: 虚线圆圈)', fontsize=14)
        axes[0].set_xlabel('X 像素')
        axes[0].set_ylabel('Y 像素')
        plt.colorbar(im1, ax=axes[0])
        
        # 右图：对数标度显示
        # 为了避免负值问题，先进行数据处理
        display_data = image_data.copy()
        min_val = np.min(display_data)
        if min_val <= 0:
            display_data = display_data - min_val + 1e-10
        
        im2 = axes[1].imshow(display_data, cmap='gray', origin='lower', norm=LogNorm())
        axes[1].set_title('对数标度显示\n(更好地显示圆圈标记)', fontsize=14)
        axes[1].set_xlabel('X 像素')
        axes[1].set_ylabel('Y 像素')
        plt.colorbar(im2, ax=axes[1])
        
        plt.tight_layout()
        
        if output_path:
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            print(f"显示图像已保存到: {output_path}")
        else:
            plt.show()
        
        plt.close()
        
    except Exception as e:
        print(f"显示FITS文件时出错: {str(e)}")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python test_circle_markers.py <marked_fits_file> [output_image]")
        print("示例: python test_circle_markers.py otrain_results/aligned_comparison_*_marked_*.fits")
        return
    
    fits_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    if not os.path.exists(fits_path):
        print(f"文件不存在: {fits_path}")
        return
    
    print(f"显示标记的FITS文件: {fits_path}")
    display_marked_fits(fits_path, output_path)

if __name__ == "__main__":
    main()
