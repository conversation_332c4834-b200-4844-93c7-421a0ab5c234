#!/usr/bin/env python3
"""
Ryan <PERSON> Style Difference Image Analysis (DIA)
基于天文学标准的差异图像分析实现

This implementation follows the DIA methodology commonly used in time-domain astronomy
for detecting transient sources and variable stars.

Author: Augment Agent
Date: 2025-07-23
"""

import os
import sys
import numpy as np
import logging
from datetime import datetime
from astropy.io import fits
from astropy.stats import sigma_clipped_stats
from astropy.convolution import Gaussian2DKernel, convolve
from photutils import DAOStarFinder, aperture_photometry, CircularAperture
from scipy import ndimage
from scipy.optimize import minimize
import cv2
import matplotlib.pyplot as plt
from matplotlib.colors import LogNorm


class RyanOelkersDIA:
    """
    Ryan Oelkers风格的差异图像分析器
    
    实现标准的天文DIA流程：
    1. 图像预处理和对齐
    2. PSF匹配
    3. 差异图像生成
    4. 瞬变源检测
    5. 光度测量
    """
    
    def __init__(self, detection_threshold=5.0, psf_matching=True):
        """
        初始化DIA分析器
        
        Args:
            detection_threshold (float): 检测阈值（sigma倍数）
            psf_matching (bool): 是否进行PSF匹配
        """
        self.detection_threshold = detection_threshold
        self.psf_matching = psf_matching
        self.setup_logging()
        
        # DIA参数
        self.dia_params = {
            'kernel_size': 21,           # 卷积核大小
            'psf_sigma': 2.0,           # PSF高斯宽度
            'background_box_size': 50,   # 背景估计盒子大小
            'aperture_radius': 5.0,     # 测光孔径半径
            'min_separation': 10,       # 最小源间距
            'fwhm': 4.0,               # 预期FWHM
        }
        
    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('ryanoelkers_dia.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def load_fits_image(self, fits_path):
        """
        加载FITS图像
        
        Args:
            fits_path (str): FITS文件路径
            
        Returns:
            tuple: (图像数据, 头信息)
        """
        try:
            with fits.open(fits_path) as hdul:
                data = hdul[0].data.astype(np.float64)
                header = hdul[0].header
                
            self.logger.info(f"成功加载FITS文件: {fits_path}")
            self.logger.info(f"图像尺寸: {data.shape}")
            self.logger.info(f"数据类型: {data.dtype}")
            
            return data, header
            
        except Exception as e:
            self.logger.error(f"加载FITS文件失败 {fits_path}: {str(e)}")
            return None, None
            
    def estimate_background(self, image_data):
        """
        估计图像背景
        
        Args:
            image_data (np.ndarray): 图像数据
            
        Returns:
            tuple: (背景值, 背景RMS, 背景图像)
        """
        try:
            # 使用sigma-clipped统计估计背景
            mean, median, std = sigma_clipped_stats(image_data, sigma=3.0, maxiters=5)
            
            # 创建背景图像（简化版，实际应用中可能需要更复杂的背景建模）
            background_image = np.full_like(image_data, median)
            
            self.logger.info(f"背景统计: mean={mean:.6f}, median={median:.6f}, std={std:.6f}")
            
            return median, std, background_image
            
        except Exception as e:
            self.logger.error(f"背景估计失败: {str(e)}")
            return 0.0, 1.0, np.zeros_like(image_data)
            
    def create_psf_kernel(self, sigma=None):
        """
        创建PSF匹配核
        
        Args:
            sigma (float): 高斯核的sigma值
            
        Returns:
            np.ndarray: PSF核
        """
        if sigma is None:
            sigma = self.dia_params['psf_sigma']
            
        kernel_size = self.dia_params['kernel_size']
        kernel = Gaussian2DKernel(sigma, x_size=kernel_size, y_size=kernel_size)
        
        return kernel.array
        
    def match_psf(self, reference_image, science_image):
        """
        PSF匹配
        
        Args:
            reference_image (np.ndarray): 参考图像
            science_image (np.ndarray): 科学图像
            
        Returns:
            np.ndarray: PSF匹配后的科学图像
        """
        if not self.psf_matching:
            return science_image
            
        try:
            # 简化的PSF匹配：使用高斯卷积
            # 实际应用中应该使用更复杂的PSF建模和匹配
            kernel = self.create_psf_kernel()
            matched_image = convolve(science_image, kernel, boundary='extend')
            
            self.logger.info("PSF匹配完成")
            return matched_image
            
        except Exception as e:
            self.logger.error(f"PSF匹配失败: {str(e)}")
            return science_image
            
    def create_difference_image(self, reference_image, science_image):
        """
        创建差异图像
        
        Args:
            reference_image (np.ndarray): 参考图像
            science_image (np.ndarray): 科学图像
            
        Returns:
            tuple: (差异图像, 差异图像误差)
        """
        try:
            # PSF匹配
            matched_science = self.match_psf(reference_image, science_image)
            
            # 估计背景
            ref_bg, ref_rms, _ = self.estimate_background(reference_image)
            sci_bg, sci_rms, _ = self.estimate_background(matched_science)
            
            # 背景减除
            ref_sub = reference_image - ref_bg
            sci_sub = matched_science - sci_bg
            
            # 创建差异图像 (Science - Reference)
            difference = sci_sub - ref_sub
            
            # 估计差异图像的误差
            # 简化版：假设泊松噪声占主导
            error = np.sqrt(np.abs(reference_image) + np.abs(matched_science) + ref_rms**2 + sci_rms**2)
            
            self.logger.info("差异图像创建完成")
            self.logger.info(f"差异图像统计: min={np.min(difference):.6f}, max={np.max(difference):.6f}")
            
            return difference, error
            
        except Exception as e:
            self.logger.error(f"差异图像创建失败: {str(e)}")
            return None, None
            
    def detect_transients(self, difference_image, error_image):
        """
        检测瞬变源
        
        Args:
            difference_image (np.ndarray): 差异图像
            error_image (np.ndarray): 误差图像
            
        Returns:
            list: 检测到的瞬变源列表
        """
        try:
            # 计算信噪比图像
            snr_image = np.abs(difference_image) / error_image
            
            # 使用DAOStarFinder检测源
            finder = DAOStarFinder(
                threshold=self.detection_threshold,
                fwhm=self.dia_params['fwhm'],
                brightest=None,
                exclude_border=True
            )
            
            sources = finder(snr_image)
            
            if sources is None:
                self.logger.info("未检测到瞬变源")
                return []
                
            # 转换为列表格式
            transients = []
            for source in sources:
                transient = {
                    'x': float(source['xcentroid']),
                    'y': float(source['ycentroid']),
                    'flux': float(difference_image[int(source['ycentroid']), int(source['xcentroid'])]),
                    'snr': float(snr_image[int(source['ycentroid']), int(source['xcentroid'])]),
                    'significance': float(source['peak'])
                }
                transients.append(transient)
                
            self.logger.info(f"检测到 {len(transients)} 个瞬变源")
            
            return transients
            
        except Exception as e:
            self.logger.error(f"瞬变源检测失败: {str(e)}")
            return []
            
    def perform_photometry(self, image, sources, error_image=None):
        """
        对检测到的源进行测光
        
        Args:
            image (np.ndarray): 图像数据
            sources (list): 源列表
            error_image (np.ndarray): 误差图像
            
        Returns:
            list: 包含测光结果的源列表
        """
        if not sources:
            return sources
            
        try:
            # 创建孔径
            positions = [(s['x'], s['y']) for s in sources]
            apertures = CircularAperture(positions, r=self.dia_params['aperture_radius'])
            
            # 执行孔径测光
            phot_table = aperture_photometry(image, apertures, error=error_image)
            
            # 更新源信息
            for i, source in enumerate(sources):
                source['aperture_flux'] = float(phot_table['aperture_sum'][i])
                if error_image is not None:
                    source['aperture_flux_err'] = float(phot_table['aperture_sum_err'][i])
                else:
                    source['aperture_flux_err'] = np.sqrt(np.abs(source['aperture_flux']))
                    
            self.logger.info(f"完成 {len(sources)} 个源的测光")
            
            return sources

        except Exception as e:
            self.logger.error(f"测光失败: {str(e)}")
            return sources

    def save_fits_result(self, data, output_path, header=None):
        """
        保存FITS格式结果

        Args:
            data (np.ndarray): 图像数据
            output_path (str): 输出路径
            header (fits.Header): FITS头信息
        """
        try:
            if header is None:
                header = fits.Header()

            # 添加处理信息到头部
            header['HISTORY'] = f'Processed by RyanOelkersDIA on {datetime.now().isoformat()}'
            header['DIASOFT'] = 'RyanOelkersDIA'
            header['DIAVERS'] = '1.0'

            hdu = fits.PrimaryHDU(data=data.astype(np.float32), header=header)
            hdu.writeto(output_path, overwrite=True)

            self.logger.info(f"FITS结果已保存: {output_path}")

        except Exception as e:
            self.logger.error(f"保存FITS文件失败 {output_path}: {str(e)}")

    def save_catalog(self, sources, output_path):
        """
        保存源目录

        Args:
            sources (list): 源列表
            output_path (str): 输出路径
        """
        try:
            with open(output_path, 'w') as f:
                # 写入头部
                f.write("# Ryan Oelkers DIA Transient Catalog\n")
                f.write(f"# Generated on {datetime.now().isoformat()}\n")
                f.write("# Columns: ID X Y FLUX SNR SIGNIFICANCE APERTURE_FLUX APERTURE_FLUX_ERR\n")

                # 写入源数据
                for i, source in enumerate(sources):
                    f.write(f"{i+1:4d} {source['x']:8.3f} {source['y']:8.3f} "
                           f"{source['flux']:12.6e} {source['snr']:8.3f} "
                           f"{source['significance']:8.3f} "
                           f"{source.get('aperture_flux', 0.0):12.6e} "
                           f"{source.get('aperture_flux_err', 0.0):12.6e}\n")

            self.logger.info(f"源目录已保存: {output_path}")

        except Exception as e:
            self.logger.error(f"保存源目录失败 {output_path}: {str(e)}")

    def create_visualization(self, reference_image, science_image, difference_image,
                           transients, output_path):
        """
        创建可视化结果

        Args:
            reference_image (np.ndarray): 参考图像
            science_image (np.ndarray): 科学图像
            difference_image (np.ndarray): 差异图像
            transients (list): 瞬变源列表
            output_path (str): 输出路径
        """
        try:
            fig, axes = plt.subplots(2, 2, figsize=(12, 12))
            fig.suptitle('Ryan Oelkers DIA Results', fontsize=16)

            # 参考图像
            im1 = axes[0, 0].imshow(reference_image, cmap='gray', origin='lower')
            axes[0, 0].set_title('Reference Image')
            axes[0, 0].set_xlabel('X (pixels)')
            axes[0, 0].set_ylabel('Y (pixels)')
            plt.colorbar(im1, ax=axes[0, 0])

            # 科学图像
            im2 = axes[0, 1].imshow(science_image, cmap='gray', origin='lower')
            axes[0, 1].set_title('Science Image')
            axes[0, 1].set_xlabel('X (pixels)')
            axes[0, 1].set_ylabel('Y (pixels)')
            plt.colorbar(im2, ax=axes[0, 1])

            # 差异图像
            vmax = np.percentile(np.abs(difference_image), 99)
            im3 = axes[1, 0].imshow(difference_image, cmap='RdBu_r', origin='lower',
                                   vmin=-vmax, vmax=vmax)
            axes[1, 0].set_title('Difference Image')
            axes[1, 0].set_xlabel('X (pixels)')
            axes[1, 0].set_ylabel('Y (pixels)')
            plt.colorbar(im3, ax=axes[1, 0])

            # 标记瞬变源的差异图像
            im4 = axes[1, 1].imshow(difference_image, cmap='RdBu_r', origin='lower',
                                   vmin=-vmax, vmax=vmax)
            axes[1, 1].set_title(f'Transients Detected ({len(transients)})')
            axes[1, 1].set_xlabel('X (pixels)')
            axes[1, 1].set_ylabel('Y (pixels)')

            # 标记瞬变源
            for transient in transients:
                circle = plt.Circle((transient['x'], transient['y']),
                                  self.dia_params['aperture_radius'],
                                  fill=False, color='yellow', linewidth=2)
                axes[1, 1].add_patch(circle)
                axes[1, 1].text(transient['x'] + 5, transient['y'] + 5,
                               f"SNR={transient['snr']:.1f}",
                               color='yellow', fontsize=8)

            plt.colorbar(im4, ax=axes[1, 1])

            plt.tight_layout()
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            plt.close()

            self.logger.info(f"可视化结果已保存: {output_path}")

        except Exception as e:
            self.logger.error(f"创建可视化失败 {output_path}: {str(e)}")

    def create_difference_visualization(self, difference_image, transients, output_path):
        """
        创建差异图像的可视化结果

        Args:
            difference_image (np.ndarray): 差异图像
            transients (list): 瞬变源列表
            output_path (str): 输出路径
        """
        try:
            fig, axes = plt.subplots(1, 2, figsize=(12, 6))
            fig.suptitle('Ryan Oelkers DIA - Difference Image Analysis', fontsize=16)

            # 差异图像
            vmax = np.percentile(np.abs(difference_image), 99)
            im1 = axes[0].imshow(difference_image, cmap='RdBu_r', origin='lower',
                               vmin=-vmax, vmax=vmax)
            axes[0].set_title('Difference Image')
            axes[0].set_xlabel('X (pixels)')
            axes[0].set_ylabel('Y (pixels)')
            plt.colorbar(im1, ax=axes[0])

            # 标记瞬变源的差异图像
            im2 = axes[1].imshow(difference_image, cmap='RdBu_r', origin='lower',
                               vmin=-vmax, vmax=vmax)
            axes[1].set_title(f'Detected Transients ({len(transients)})')
            axes[1].set_xlabel('X (pixels)')
            axes[1].set_ylabel('Y (pixels)')

            # 标记瞬变源
            for i, transient in enumerate(transients):
                # 根据流量正负使用不同颜色
                color = 'yellow' if transient['flux'] > 0 else 'cyan'
                circle = plt.Circle((transient['x'], transient['y']),
                                  self.dia_params['aperture_radius'],
                                  fill=False, color=color, linewidth=2)
                axes[1].add_patch(circle)
                axes[1].text(transient['x'] + 8, transient['y'] + 8,
                           f"{i+1}\nSNR={transient['snr']:.1f}",
                           color=color, fontsize=8, fontweight='bold')

            plt.colorbar(im2, ax=axes[1])

            # 添加图例
            from matplotlib.patches import Patch
            legend_elements = [
                Patch(facecolor='yellow', label='Positive flux'),
                Patch(facecolor='cyan', label='Negative flux')
            ]
            axes[1].legend(handles=legend_elements, loc='upper right')

            plt.tight_layout()
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            plt.close()

            self.logger.info(f"差异图像可视化结果已保存: {output_path}")

        except Exception as e:
            self.logger.error(f"创建差异图像可视化失败 {output_path}: {str(e)}")

    def create_marked_fits(self, image_data, transients, output_path):
        """
        创建带有圆圈标记的FITS文件，圆圈大小根据SNR调整

        Args:
            image_data (np.ndarray): 原始图像数据
            transients (list): 瞬变源列表
            output_path (str): 输出路径
        """
        try:
            # 创建标记图像的副本
            marked_data = image_data.copy()

            # 获取图像尺寸
            height, width = image_data.shape

            # 计算SNR范围用于标准化圆圈大小
            if not transients:
                self.logger.warning("没有瞬变源需要标记")
                return

            snr_values = [t['snr'] for t in transients]
            min_snr = min(snr_values)
            max_snr = max(snr_values)

            # 定义圆圈大小范围
            min_radius = 3   # 最小圆圈半径
            max_radius = 15  # 最大圆圈半径

            self.logger.info(f"标记 {len(transients)} 个瞬变源，SNR范围: {min_snr:.1f} - {max_snr:.1f}")

            # 为每个瞬变源绘制圆圈
            for i, transient in enumerate(transients):
                x = int(round(transient['x']))
                y = int(round(transient['y']))
                snr = transient['snr']
                flux = transient['flux']

                # 根据SNR计算圆圈半径
                if max_snr > min_snr:
                    # 标准化SNR到0-1范围
                    normalized_snr = (snr - min_snr) / (max_snr - min_snr)
                else:
                    normalized_snr = 0.5

                # 计算圆圈半径
                radius = int(min_radius + normalized_snr * (max_radius - min_radius))

                # 根据流量正负确定圆圈值
                # 正流量用高值，负流量用低值
                if flux > 0:
                    circle_value = np.max(image_data) * 1.2  # 比最大值高20%
                else:
                    circle_value = np.min(image_data) * 1.2  # 比最小值低20%

                # 绘制圆圈
                self._draw_circle(marked_data, x, y, radius, circle_value)

                # 在圆圈旁边添加编号（可选）
                # 这里我们通过在圆圈中心附近设置特殊值来标记
                if 0 <= x < width and 0 <= y < height:
                    # 在圆圈中心放置一个小点作为标记
                    marked_data[y, x] = circle_value * 0.8

            self.logger.info(f"完成瞬变源标记，圆圈半径范围: {min_radius} - {max_radius} 像素")

            # 保存标记后的FITS文件
            header = fits.Header()
            header['HISTORY'] = f'Marked by RyanOelkersDIA on {datetime.now().isoformat()}'
            header['DIASOFT'] = 'RyanOelkersDIA'
            header['DIAVERS'] = '1.0'
            header['MARKED'] = 'TRUE'
            header['NSOURCES'] = len(transients)
            header['MINSNR'] = min_snr
            header['MAXSNR'] = max_snr
            header['MINRAD'] = min_radius
            header['MAXRAD'] = max_radius
            header['COMMENT'] = 'Circle size proportional to SNR'

            hdu = fits.PrimaryHDU(data=marked_data.astype(np.float32), header=header)
            hdu.writeto(output_path, overwrite=True)

            self.logger.info(f"标记FITS文件已保存: {output_path}")

        except Exception as e:
            self.logger.error(f"创建标记FITS文件失败 {output_path}: {str(e)}")

    def _draw_circle(self, image, center_x, center_y, radius, value):
        """
        在图像上绘制圆圈（优化版本）

        Args:
            image (np.ndarray): 图像数组
            center_x (int): 圆心X坐标
            center_y (int): 圆心Y坐标
            radius (int): 圆圈半径
            value (float): 圆圈像素值
        """
        height, width = image.shape

        # 边界检查
        if center_x < 0 or center_x >= width or center_y < 0 or center_y >= height:
            return

        # 只在圆圈周围的局部区域工作，提高效率
        margin = radius + 2
        x_min = max(0, center_x - margin)
        x_max = min(width, center_x + margin + 1)
        y_min = max(0, center_y - margin)
        y_max = min(height, center_y + margin + 1)

        # 创建局部坐标网格
        y_local, x_local = np.ogrid[y_min:y_max, x_min:x_max]

        # 计算到圆心的距离
        distances = np.sqrt((x_local - center_x)**2 + (y_local - center_y)**2)

        # 创建圆圈掩码（圆环，厚度为1像素）
        circle_mask = (distances >= radius - 0.5) & (distances <= radius + 0.5)

        # 应用圆圈到局部区域
        image[y_min:y_max, x_min:x_max][circle_mask] = value
    def process_difference_fits(self, difference_fits, output_dir=None):
        """
        直接处理差异图像FITS文件

        Args:
            difference_fits (str): 差异图像FITS文件路径
            output_dir (str): 输出目录

        Returns:
            dict: 处理结果
        """
        try:
            self.logger.info("=" * 60)
            self.logger.info("开始Ryan Oelkers DIA差异图像处理")
            self.logger.info("=" * 60)

            # 设置输出目录
            if output_dir is None:
                output_dir = os.path.dirname(difference_fits)
            os.makedirs(output_dir, exist_ok=True)

            # 1. 加载差异图像
            self.logger.info("步骤1: 加载差异图像")
            diff_data, diff_header = self.load_fits_image(difference_fits)

            if diff_data is None:
                self.logger.error("差异图像加载失败")
                return None

            # 2. 估计差异图像的误差
            self.logger.info("步骤2: 估计差异图像误差")
            # 对于差异图像，使用简化的误差估计
            _, diff_rms, _ = self.estimate_background(diff_data)
            error_image = np.full_like(diff_data, diff_rms)

            # 3. 检测瞬变源
            self.logger.info("步骤3: 检测瞬变源")
            transients = self.detect_transients(diff_data, error_image)

            # 4. 测光
            self.logger.info("步骤4: 执行测光")
            transients = self.perform_photometry(diff_data, transients, error_image)

            # 5. 保存结果
            self.logger.info("步骤5: 保存结果")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_name = f"ryanoelkers_dia_diff_{timestamp}"

            # 保存源目录
            catalog_path = os.path.join(output_dir, f"{base_name}_transients.txt")
            self.save_catalog(transients, catalog_path)

            # 创建带标记的FITS文件
            marked_fits_path = os.path.join(output_dir, f"{base_name}_marked.fits")
            self.create_marked_fits(diff_data, transients, marked_fits_path)

            # 创建可视化（使用差异图像作为所有面板）
            viz_path = os.path.join(output_dir, f"{base_name}_visualization.png")
            self.create_difference_visualization(diff_data, transients, viz_path)

            # 返回结果
            result = {
                'success': True,
                'transients_detected': len(transients),
                'transients': transients,
                'output_directory': output_dir,
                'difference_fits': difference_fits,
                'marked_fits': marked_fits_path,
                'catalog_file': catalog_path,
                'visualization': viz_path,
                'processing_mode': 'difference_only'
            }

            self.logger.info(f"差异图像DIA处理完成，检测到 {len(transients)} 个瞬变源")
            return result

        except Exception as e:
            self.logger.error(f"差异图像DIA处理失败: {str(e)}")
            return None

    def process_dia(self, reference_fits, science_fits, output_dir=None):
        """
        执行完整的DIA处理流程

        Args:
            reference_fits (str): 参考图像FITS文件路径
            science_fits (str): 科学图像FITS文件路径
            output_dir (str): 输出目录

        Returns:
            dict: 处理结果
        """
        try:
            self.logger.info("=" * 60)
            self.logger.info("开始Ryan Oelkers DIA处理")
            self.logger.info("=" * 60)

            # 设置输出目录
            if output_dir is None:
                output_dir = os.path.dirname(science_fits)
            os.makedirs(output_dir, exist_ok=True)

            # 1. 加载图像
            self.logger.info("步骤1: 加载FITS图像")
            ref_data, ref_header = self.load_fits_image(reference_fits)
            sci_data, sci_header = self.load_fits_image(science_fits)

            if ref_data is None or sci_data is None:
                self.logger.error("图像加载失败")
                return None

            # 2. 创建差异图像
            self.logger.info("步骤2: 创建差异图像")
            diff_image, error_image = self.create_difference_image(ref_data, sci_data)

            if diff_image is None:
                self.logger.error("差异图像创建失败")
                return None

            # 3. 检测瞬变源
            self.logger.info("步骤3: 检测瞬变源")
            transients = self.detect_transients(diff_image, error_image)

            # 4. 测光
            self.logger.info("步骤4: 执行测光")
            transients = self.perform_photometry(diff_image, transients, error_image)

            # 5. 保存结果
            self.logger.info("步骤5: 保存结果")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_name = f"ryanoelkers_dia_{timestamp}"

            # 保存差异图像
            diff_fits_path = os.path.join(output_dir, f"{base_name}_difference.fits")
            self.save_fits_result(diff_image, diff_fits_path, sci_header)

            # 保存源目录
            catalog_path = os.path.join(output_dir, f"{base_name}_transients.txt")
            self.save_catalog(transients, catalog_path)

            # 创建带标记的FITS文件
            marked_fits_path = os.path.join(output_dir, f"{base_name}_marked.fits")
            self.create_marked_fits(diff_image, transients, marked_fits_path)

            # 创建可视化
            viz_path = os.path.join(output_dir, f"{base_name}_visualization.png")
            self.create_visualization(ref_data, sci_data, diff_image, transients, viz_path)

            # 返回结果
            result = {
                'success': True,
                'transients_detected': len(transients),
                'transients': transients,
                'output_directory': output_dir,
                'difference_fits': diff_fits_path,
                'marked_fits': marked_fits_path,
                'catalog_file': catalog_path,
                'visualization': viz_path,
                'reference_file': reference_fits,
                'science_file': science_fits
            }

            self.logger.info(f"DIA处理完成，检测到 {len(transients)} 个瞬变源")
            return result

        except Exception as e:
            self.logger.error(f"DIA处理失败: {str(e)}")
            return None
