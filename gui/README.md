# FITS文件网页下载器GUI

一个用于扫描网页、下载FITS文件并显示图像的图形用户界面应用程序。

## 📁 项目结构

```
gui/
├── fits_web_downloader.py    # 主GUI应用程序
├── web_scanner.py           # 网页扫描模块
├── fits_viewer.py           # FITS图像查看器
├── config_manager.py        # 配置文件管理器
├── url_builder.py           # URL构建器组件
├── calendar_widget.py       # 日历选择器组件
├── filename_parser.py       # FITS文件名解析器
├── diff_orb_integration.py  # diff_orb集成模块
├── requirements.txt         # 依赖包列表
├── gui_config.json.template # 配置文件模板
├── README.md               # 使用说明（本文件）
├── USAGE.md                # 详细使用指南
├── DIFF_INTEGRATION_GUIDE.md # diff功能使用指南
├── run_gui.py              # 启动脚本
├── test_gui.py             # 测试脚本
├── test_diff_integration.py # diff集成测试脚本
└── demo.py                 # 演示脚本
```

## 🚀 主要功能

### 1. 智能URL构建
- 支持KATS数据URL自动构建：`https://download.china-vo.org/psp/KATS/{tel_name}-DATA/{YYYYMMDD}/{K0??}/`
- 望远镜选择：GY1, GY2, GY3, GY4, GY5, GY6
- **日历式日期选择**：直观的日历界面，支持月份导航和快速选择
- 天区选择：K001 - K099
- 配置文件自动保存上次选择的参数

### 2. 网页扫描
- 扫描构建的URL获取可下载的FITS文件列表
- 支持Apache风格的目录列表页面
- 自动过滤不需要的文件（Calibration、_FZ、Flat、fiel等）
- 显示文件大小信息

### 3. 智能目录结构
- **分层目录组织**: 文件按照 `根目录/望远镜名/日期/天区/` 结构保存
- **自动目录创建**: 根据选择的参数自动创建对应目录
- **示例结构**: `D:/FITS_Data/GY5/20250718/K096/filename.fits`
- **便于管理**: 不同望远镜、日期、天区的数据分别存储

### 4. 批量下载
- 支持多选文件进行批量下载
- 并发数限制：1-3（默认1，适合天文数据下载）
- 可配置重试次数、超时时间
- 实时显示下载进度
- 自动跳过已存在的文件
- 配置自动保存

### 5. 增强的图像查看器
- **双目录支持**: 同时显示下载目录和模板文件目录
- **分层目录树**: 下载目录按望远镜/日期/天区组织，模板目录按实际结构组织
- **延迟显示**: 点击文件只选择不显示，需点击"显示图像"按钮才加载（提升响应速度）
- **diff_orb集成**: 对下载文件执行差异检测，自动匹配模板文件进行比较
- **智能文件匹配**: 根据文件名中的望远镜和K序号自动查找对应模板
- **新亮点检测**: 检测观测图像中的新发现天体或变化
- **目录管理**: 双击目录节点在文件管理器中打开
- **快速导航**: "打开下载目录"按钮直接打开当前选择对应的目录
- **目录控制**: 刷新、展开全部、折叠全部等目录树操作
- **多种显示模式**: 线性、对数、平方根、反双曲正弦
- **多种颜色映射**: gray、viridis、plasma、inferno等
- **图像统计**: 显示均值、中位数、标准差等统计信息

### 5. 配置管理
- 自动保存用户选择的参数
- 支持配置文件导入导出
- 记住上次的望远镜、日期、天区选择
- 记住下载目录和下载参数

### 6. 日志系统
- 实时显示操作日志
- 支持保存日志到文件
- 清除日志功能

## 📦 安装依赖

```bash
cd gui
pip install -r requirements.txt
```

## 🎯 使用方法

### 1. 启动应用程序

```bash
python fits_web_downloader.py
```

或使用启动脚本：

```bash
python run_gui.py
```

### 2. 构建和扫描URL

1. 在"扫描和下载"标签页的URL构建器中：
   - 选择望远镜：GY1-GY6（默认GY5）
   - 选择日期：点击📅按钮打开日历选择器，或点击"今天"按钮
   - 选择天区：K001-K099（默认K096）
2. URL会自动构建并显示
3. 点击"扫描FITS文件"按钮
4. 等待扫描完成，文件列表将显示在下方

### 3. 下载文件

1. 在文件列表中选择要下载的文件（可使用全选、全不选、反选按钮）
2. 设置下载根目录和模板文件目录
3. 配置下载参数（并发数、重试次数、超时时间）
4. 点击"开始下载"按钮
5. 观察进度条和状态信息
6. 文件将自动保存到 `根目录/望远镜名/日期/天区/` 结构中

### 4. 查看图像

1. 切换到"图像查看"标签页
2. 使用左侧目录树浏览FITS文件：
   - 📁 下载目录：按照望远镜/日期/天区层次显示
   - 📋 模板目录：按照实际文件夹结构显示
   - 点击FITS文件节点选择文件（不立即显示）
   - 点击"显示图像"按钮加载和显示选中的图像
   - 点击"执行Diff"按钮对下载文件进行差异检测（仅下载目录文件可用）
   - 双击目录节点在文件管理器中打开
3. 使用"打开下载目录"按钮快速访问当前选择对应的目录
4. 调整显示模式和颜色映射
5. 可以保存图像到本地

### 5. 查看日志

1. 切换到"日志"标签页查看详细的操作日志
2. 可以清除日志或保存日志到文件

## ⚙️ 配置说明

### 下载参数
- **并发数**: 同时下载的文件数量（1-10，默认4）
- **重试次数**: 下载失败时的重试次数（1-10，默认3）
- **超时时间**: 单个文件下载的超时时间（10-120秒，默认30）

### 显示模式
- **linear**: 线性显示（默认）
- **log**: 对数显示（适合高动态范围图像）
- **sqrt**: 平方根显示
- **asinh**: 反双曲正弦显示

### 颜色映射
- **gray**: 灰度（默认）
- **viridis**: 紫绿色映射
- **plasma**: 等离子色映射
- **inferno**: 地狱色映射
- **hot**: 热色映射
- **cool**: 冷色映射

## 🔧 技术特性

### 网页扫描
- 支持BeautifulSoup HTML解析
- 支持正则表达式匹配文件链接
- 自动处理相对URL和绝对URL
- 支持HEAD请求获取文件大小

### 下载功能
- 基于现有的`data_collect/data_02_download.py`模块
- 支持断点续传（跳过已存在文件）
- 多线程并发下载
- 指数退避重试机制

### 图像处理
- 基于astropy处理FITS文件
- 支持3D数据自动降维
- sigma-clipped统计计算
- matplotlib图像显示

## 🐛 故障排除

### 常见问题

1. **扫描失败**
   - 检查网络连接
   - 确认URL格式正确
   - 查看日志了解详细错误信息

2. **下载失败**
   - 检查下载目录权限
   - 调整超时时间和重试次数
   - 减少并发数

3. **图像显示异常**
   - 确认FITS文件格式正确
   - 尝试不同的显示模式
   - 检查文件是否损坏

### 日志分析
- 所有操作都会记录在日志中
- 错误信息包含详细的堆栈跟踪
- 可以保存日志进行进一步分析

## 📝 注意事项

1. **网络要求**: 需要稳定的网络连接访问目标网站
2. **存储空间**: FITS文件通常较大，确保有足够的磁盘空间
3. **系统资源**: 高并发下载会消耗较多CPU和内存资源
4. **文件权限**: 确保对下载目录有写入权限

## 🔄 更新日志

### v1.0.0 (2025-01-18)
- 初始版本发布
- 支持网页扫描和批量下载
- 集成FITS图像查看器
- 完整的GUI界面和日志系统
