# FITS文件网页下载器GUI - 使用指南

## 🎯 快速开始

### 1. 启动应用程序
```bash
cd gui
python fits_web_downloader.py
```

或使用启动脚本：
```bash
python run_gui.py
```

### 2. 基本使用流程

#### 步骤1：扫描FITS文件
1. 在"扫描和下载"标签页中，输入要扫描的URL
   - 默认URL：`https://download.china-vo.org/psp/KATS/GY5-DATA/20250701/K096/`
2. 点击"扫描"按钮
3. 等待扫描完成，文件列表将显示所有可下载的FITS文件

#### 步骤2：选择和下载文件
1. 在文件列表中点击文件名前的复选框选择文件
   - 使用"全选"、"全不选"、"反选"按钮快速操作
2. 点击"浏览"选择下载目录
3. 配置下载参数（可选）：
   - 并发数：同时下载的文件数量（默认4）
   - 重试次数：下载失败时的重试次数（默认3）
   - 超时时间：单个文件下载超时时间（默认30秒）
4. 点击"开始下载"
5. 观察进度条和状态信息

#### 步骤3：查看FITS图像
1. 切换到"图像查看"标签页
2. 点击"选择FITS文件"选择要查看的文件
   - 或点击"从下载目录选择"从已下载的文件中选择
3. 调整显示设置：
   - 显示模式：linear（线性）、log（对数）、sqrt（平方根）、asinh（反双曲正弦）
   - 颜色映射：gray、viridis、plasma、inferno、hot、cool
4. 查看图像统计信息
5. 可以保存图像为PNG、JPEG或PDF格式

## 🔧 高级功能

### 网页扫描
- 支持Apache风格的目录列表页面
- 自动过滤不需要的文件类型（Calibration、_FZ、Flat、fiel等）
- 显示文件大小信息
- 支持多种FITS文件扩展名（.fits、.fit、.fts）

### 批量下载
- 多线程并发下载，提高效率
- 自动跳过已存在的文件
- 指数退避重试机制
- 实时进度显示和状态更新

### 图像显示
- 支持3D FITS数据（自动取第一个切片）
- sigma-clipped统计计算
- 多种数据变换和颜色映射
- 交互式图像显示和缩放

### 日志系统
- 详细的操作日志记录
- 实时日志显示
- 支持保存日志到文件
- 错误信息和调试信息

## 📋 测试和验证

### 运行测试
```bash
python test_gui.py
```

### 运行演示
```bash
python demo.py
```

## 🚨 故障排除

### 常见问题

1. **扫描失败**
   ```
   错误：网络请求失败
   解决：检查网络连接，确认URL可访问
   ```

2. **下载失败**
   ```
   错误：下载超时
   解决：增加超时时间，减少并发数
   ```

3. **图像显示异常**
   ```
   错误：无法读取图像数据
   解决：确认FITS文件格式正确，尝试不同显示模式
   ```

4. **GUI启动失败**
   ```
   错误：导入模块失败
   解决：安装缺失的依赖包
   pip install -r requirements.txt
   ```

### 调试技巧

1. **查看详细日志**
   - 切换到"日志"标签页查看详细信息
   - 保存日志文件进行分析

2. **测试网络连接**
   ```bash
   curl -I https://download.china-vo.org/psp/KATS/GY5-DATA/20250701/K096/
   ```

3. **验证FITS文件**
   ```python
   from astropy.io import fits
   with fits.open('filename.fits') as hdul:
       print(hdul.info())
   ```

## 🔗 相关文件

- `fits_web_downloader.py` - 主GUI应用程序
- `web_scanner.py` - 网页扫描模块
- `fits_viewer.py` - FITS图像查看器
- `requirements.txt` - 依赖包列表
- `test_gui.py` - 测试脚本
- `demo.py` - 演示脚本
- `run_gui.py` - 启动脚本

## 📞 技术支持

如果遇到问题：

1. 首先运行测试脚本确认环境配置
2. 查看日志标签页的详细错误信息
3. 检查网络连接和URL可访问性
4. 确认有足够的磁盘空间
5. 尝试调整下载参数（并发数、超时时间等）

## 🎨 界面截图说明

### 扫描和下载标签页
- URL输入框：输入要扫描的网页地址
- 扫描按钮：开始扫描FITS文件
- 文件列表：显示找到的FITS文件，支持多选
- 下载设置：配置下载目录和参数
- 进度条：显示下载进度

### 图像查看标签页
- 文件选择：选择要查看的FITS文件
- 图像显示区域：显示FITS图像和颜色条
- 控制面板：调整显示模式和颜色映射
- 统计信息：显示图像的统计数据

### 日志标签页
- 日志显示区域：实时显示操作日志
- 日志控制：清除日志、保存日志功能

## 🔄 更新和维护

定期更新依赖包：
```bash
pip install --upgrade -r requirements.txt
```

检查新版本功能和修复。
