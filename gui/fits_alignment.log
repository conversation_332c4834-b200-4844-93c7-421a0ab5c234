2025-07-21 10:23:14,265 - INFO - 开始diff操作:
2025-07-21 10:23:14,266 - INFO -   参考文件 (模板): template.fits
2025-07-21 10:23:14,266 - INFO -   待比较文件 (下载): observation.fits
2025-07-21 10:23:14,266 - INFO -   输出目录: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uwkh3ml3\results
2025-07-21 10:23:14,266 - INFO - ============================================================
2025-07-21 10:23:14,266 - INFO - 开始FITS图像对齐和差异检测
2025-07-21 10:23:14,266 - INFO - ============================================================
2025-07-21 10:23:14,266 - INFO - 步骤1: 加载FITS图像
2025-07-21 10:23:14,267 - INFO - 加载FITS文件: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uwkh3ml3\template.fits
2025-07-21 10:23:14,270 - INFO - 使用整个图像进行分析: 300×300 像素
2025-07-21 10:23:14,270 - INFO - 图像加载成功: (300, 300)
2025-07-21 10:23:14,270 - INFO - 加载FITS文件: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uwkh3ml3\observation.fits
2025-07-21 10:23:14,272 - INFO - 使用整个图像进行分析: 300×300 像素
2025-07-21 10:23:14,272 - INFO - 图像加载成功: (300, 300)
2025-07-21 10:23:14,273 - INFO - 步骤2: 图像预处理
2025-07-21 10:23:14,275 - INFO - 图像预处理完成
2025-07-21 10:23:14,276 - INFO - 图像预处理完成
2025-07-21 10:23:14,276 - INFO - 步骤3: 特征检测和匹配
2025-07-21 10:23:14,625 - INFO - 检测到特征点: 图像1=226, 图像2=15
2025-07-21 10:23:14,630 - INFO - 找到 10 个匹配点
2025-07-21 10:23:14,630 - INFO - 匹配质量分析:
2025-07-21 10:23:14,630 - INFO -   总匹配数: 10
2025-07-21 10:23:14,631 - INFO -   平均距离: 82.00
2025-07-21 10:23:14,631 - INFO -   最小距离: 71.00
2025-07-21 10:23:14,631 - INFO -   最大距离: 87.00
2025-07-21 10:23:14,631 - INFO -   距离标准差: 4.77
2025-07-21 10:23:14,631 - INFO - 匹配点空间分布:
2025-07-21 10:23:14,631 - INFO -   图像1分布范围: X=182.8, Y=170.0
2025-07-21 10:23:14,632 - INFO -   图像2分布范围: X=51.0, Y=100.8
2025-07-21 10:23:14,632 - INFO - ✅ 匹配点数量充足
2025-07-21 10:23:14,632 - WARNING - ⚠️  平均匹配距离较大，匹配质量可能不佳
2025-07-21 10:23:14,632 - INFO - 步骤4: 图像对齐
2025-07-21 10:23:14,632 - INFO - 使用刚体变换（仅平移和旋转）
2025-07-21 10:23:14,633 - INFO - 变换分析:
2025-07-21 10:23:14,633 - INFO -   平移: (-121.63, 267.03) 像素
2025-07-21 10:23:14,633 - INFO -   旋转: 74.85 度
2025-07-21 10:23:14,633 - INFO -   缩放: X=1.5233, Y=1.5233
2025-07-21 10:23:14,633 - INFO -   变换类型: 相似变换（平移、旋转和等比缩放）
2025-07-21 10:23:14,634 - INFO - 图像对齐完成（使用天文图像友好的变换）
2025-07-21 10:23:14,635 - INFO - 已将变换应用到原始图像
2025-07-21 10:23:14,635 - INFO - 步骤5: 差异检测
2025-07-21 10:23:14,639 - INFO - 检测到 196 个新亮点
2025-07-21 10:23:14,639 - INFO - 步骤6: 标记新亮点
2025-07-21 10:23:14,643 - INFO - 标记了 196 个新亮点
2025-07-21 10:23:14,644 - INFO - 步骤8: 保存结果
2025-07-21 10:23:14,659 - INFO - 已保存参考图像FITS文件: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uwkh3ml3\results\comparison_reference_template_20250721_102314.fits
2025-07-21 10:23:14,662 - INFO - 已保存对齐后图像FITS文件: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uwkh3ml3\results\comparison_aligned_observation_20250721_102314.fits
2025-07-21 10:23:14,664 - INFO - 所有结果已保存到目录: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uwkh3ml3\results
2025-07-21 10:23:14,664 - INFO - ============================================================
2025-07-21 10:23:14,664 - INFO - FITS图像对齐和差异检测完成
2025-07-21 10:23:14,664 - INFO - 检测到 196 个新亮点
2025-07-21 10:23:14,664 - INFO - ============================================================
2025-07-21 10:23:14,665 - INFO - diff操作成功完成
2025-07-21 10:23:14,665 - INFO -   对齐成功: True
2025-07-21 10:23:14,665 - INFO -   检测到新亮点: 196 个
2025-07-21 10:23:14,666 - INFO - 扫描输出目录: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uwkh3ml3\results
2025-07-21 10:23:14,666 - INFO - 找到 8 个文件
2025-07-21 10:23:14,667 - INFO - 找到对齐FITS文件: comparison_aligned_observation_20250721_102314.fits
2025-07-21 10:23:14,667 - INFO - 找到报告文件: comparison_bright_spots_20250721_102314.txt
2025-07-21 10:23:14,667 - INFO - 找到参考FITS文件: comparison_reference_template_20250721_102314.fits
2025-07-21 10:23:14,668 - INFO - 收集到 3 个输出文件: ['aligned_fits', 'report_txt', 'reference_fits']
2025-07-21 10:23:14,685 - INFO - 扫描输出目录: C:\Users\<USER>\AppData\Local\Temp\file_collection_test_gn36h92n
2025-07-21 10:23:14,685 - INFO - 找到 9 个文件
2025-07-21 10:23:14,686 - INFO - 找到对齐FITS文件: aligned_image.fits
2025-07-21 10:23:14,686 - INFO - 找到报告文件: bright_spots_report.txt
2025-07-21 10:23:14,686 - INFO - 找到标记FITS文件: output_marked.fits
2025-07-21 10:23:14,686 - INFO - 找到参考FITS文件: reference_template.fits
2025-07-21 10:23:14,686 - INFO - 找到差异FITS文件: result_diff.fits
2025-07-21 10:23:14,687 - INFO - 未分类的FITS文件: some_other_file.fits
2025-07-21 10:23:14,687 - INFO - 找到差异FITS文件: test_difference.fits
2025-07-21 10:23:14,688 - INFO - 收集到 5 个输出文件: ['aligned_fits', 'report_txt', 'marked_fits', 'reference_fits', 'difference_fits']
2025-07-21 10:25:12,897 - INFO - 开始diff操作:
2025-07-21 10:25:12,897 - INFO -   参考文件 (模板): template.fits
2025-07-21 10:25:12,897 - INFO -   待比较文件 (下载): observation.fits
2025-07-21 10:25:12,898 - INFO -   输出目录: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_8mxgh9_w\results
2025-07-21 10:25:12,898 - INFO - ============================================================
2025-07-21 10:25:12,898 - INFO - 开始FITS图像对齐和差异检测
2025-07-21 10:25:12,898 - INFO - ============================================================
2025-07-21 10:25:12,898 - INFO - 步骤1: 加载FITS图像
2025-07-21 10:25:12,898 - INFO - 加载FITS文件: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_8mxgh9_w\template.fits
2025-07-21 10:25:12,899 - INFO - 使用整个图像进行分析: 300×300 像素
2025-07-21 10:25:12,899 - INFO - 图像加载成功: (300, 300)
2025-07-21 10:25:12,900 - INFO - 加载FITS文件: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_8mxgh9_w\observation.fits
2025-07-21 10:25:12,902 - INFO - 使用整个图像进行分析: 300×300 像素
2025-07-21 10:25:12,902 - INFO - 图像加载成功: (300, 300)
2025-07-21 10:25:12,903 - INFO - 步骤2: 图像预处理
2025-07-21 10:25:12,905 - INFO - 图像预处理完成
2025-07-21 10:25:12,906 - INFO - 图像预处理完成
2025-07-21 10:25:12,906 - INFO - 步骤3: 特征检测和匹配
2025-07-21 10:25:13,102 - INFO - 检测到特征点: 图像1=266, 图像2=15
2025-07-21 10:25:13,107 - INFO - 找到 11 个匹配点
2025-07-21 10:25:13,107 - INFO - 匹配质量分析:
2025-07-21 10:25:13,108 - INFO -   总匹配数: 11
2025-07-21 10:25:13,108 - INFO -   平均距离: 82.64
2025-07-21 10:25:13,108 - INFO -   最小距离: 70.00
2025-07-21 10:25:13,108 - INFO -   最大距离: 89.00
2025-07-21 10:25:13,108 - INFO -   距离标准差: 5.43
2025-07-21 10:25:13,109 - INFO - 匹配点空间分布:
2025-07-21 10:25:13,109 - INFO -   图像1分布范围: X=225.2, Y=216.0
2025-07-21 10:25:13,109 - INFO -   图像2分布范围: X=51.0, Y=101.2
2025-07-21 10:25:13,109 - INFO - ✅ 匹配点数量充足
2025-07-21 10:25:13,109 - WARNING - ⚠️  平均匹配距离较大，匹配质量可能不佳
2025-07-21 10:25:13,109 - INFO - 步骤4: 图像对齐
2025-07-21 10:25:13,110 - INFO - 使用刚体变换（仅平移和旋转）
2025-07-21 10:25:13,110 - INFO - 变换分析:
2025-07-21 10:25:13,111 - INFO -   平移: (66.28, 234.43) 像素
2025-07-21 10:25:13,111 - INFO -   旋转: 115.67 度
2025-07-21 10:25:13,111 - INFO -   缩放: X=0.4991, Y=0.4991
2025-07-21 10:25:13,111 - INFO -   变换类型: 相似变换（平移、旋转和等比缩放）
2025-07-21 10:25:13,111 - INFO - 图像对齐完成（使用天文图像友好的变换）
2025-07-21 10:25:13,112 - INFO - 已将变换应用到原始图像
2025-07-21 10:25:13,112 - INFO - 步骤5: 差异检测
2025-07-21 10:25:13,113 - INFO - 检测到 0 个新亮点
2025-07-21 10:25:13,114 - INFO - 步骤6: 标记新亮点
2025-07-21 10:25:13,114 - INFO - 标记了 0 个新亮点
2025-07-21 10:25:13,114 - INFO - 步骤8: 保存结果
2025-07-21 10:25:13,127 - INFO - 已保存参考图像FITS文件: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_8mxgh9_w\results\comparison_reference_template_20250721_102513.fits
2025-07-21 10:25:13,130 - INFO - 已保存对齐后图像FITS文件: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_8mxgh9_w\results\comparison_aligned_observation_20250721_102513.fits
2025-07-21 10:25:13,131 - INFO - 所有结果已保存到目录: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_8mxgh9_w\results
2025-07-21 10:25:13,131 - INFO - ============================================================
2025-07-21 10:25:13,131 - INFO - FITS图像对齐和差异检测完成
2025-07-21 10:25:13,131 - INFO - 检测到 0 个新亮点
2025-07-21 10:25:13,131 - INFO - ============================================================
2025-07-21 10:25:13,132 - INFO - diff操作成功完成
2025-07-21 10:25:13,132 - INFO -   对齐成功: True
2025-07-21 10:25:13,132 - INFO -   检测到新亮点: 0 个
2025-07-21 10:25:13,132 - INFO - 扫描输出目录: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_8mxgh9_w\results
2025-07-21 10:25:13,133 - INFO - 找到 8 个文件
2025-07-21 10:25:13,133 - INFO - 找到对齐FITS文件: comparison_aligned_observation_20250721_102513.fits
2025-07-21 10:25:13,133 - INFO - 找到差异PNG文件: comparison_binary_diff_20250721_102513.png
2025-07-21 10:25:13,133 - INFO - 找到报告文件: comparison_bright_spots_20250721_102513.txt
2025-07-21 10:25:13,134 - INFO - 找到差异PNG文件: comparison_difference_20250721_102513.png
2025-07-21 10:25:13,134 - INFO - 找到标记PNG文件: comparison_marked_20250721_102513.png
2025-07-21 10:25:13,134 - INFO - 找到参考FITS文件: comparison_reference_template_20250721_102513.fits
2025-07-21 10:25:13,134 - INFO - 收集到 5 个输出文件: ['aligned_fits', 'difference_png', 'report_txt', 'marked_png', 'reference_fits']
2025-07-21 10:25:13,150 - INFO - 扫描输出目录: C:\Users\<USER>\AppData\Local\Temp\file_collection_test_ztul2u31
2025-07-21 10:25:13,150 - INFO - 找到 9 个文件
2025-07-21 10:25:13,150 - INFO - 找到对齐FITS文件: aligned_image.fits
2025-07-21 10:25:13,150 - INFO - 找到报告文件: bright_spots_report.txt
2025-07-21 10:25:13,151 - INFO - 找到标记FITS文件: output_marked.fits
2025-07-21 10:25:13,151 - INFO - 找到参考FITS文件: reference_template.fits
2025-07-21 10:25:13,151 - INFO - 找到差异FITS文件: result_diff.fits
2025-07-21 10:25:13,151 - INFO - 未分类的FITS文件: some_other_file.fits
2025-07-21 10:25:13,152 - INFO - 找到差异FITS文件: test_difference.fits
2025-07-21 10:25:13,152 - INFO - 收集到 5 个输出文件: ['aligned_fits', 'report_txt', 'marked_fits', 'reference_fits', 'difference_fits']
2025-07-21 10:36:20,395 - INFO - 开始diff操作:
2025-07-21 10:36:20,395 - INFO -   参考文件 (模板): template.fits
2025-07-21 10:36:20,396 - INFO -   待比较文件 (下载): observation.fits
2025-07-21 10:36:20,396 - INFO -   输出目录: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uoimg1bh\results
2025-07-21 10:36:20,396 - INFO - 步骤1: 执行图像对齐...
2025-07-21 10:36:20,396 - INFO - ============================================================
2025-07-21 10:36:20,396 - INFO - 开始FITS图像对齐和差异检测
2025-07-21 10:36:20,396 - INFO - ============================================================
2025-07-21 10:36:20,396 - INFO - 步骤1: 加载FITS图像
2025-07-21 10:36:20,396 - INFO - 加载FITS文件: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uoimg1bh\template.fits
2025-07-21 10:36:20,398 - INFO - 使用整个图像进行分析: 300×300 像素
2025-07-21 10:36:20,398 - INFO - 图像加载成功: (300, 300)
2025-07-21 10:36:20,398 - INFO - 加载FITS文件: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uoimg1bh\observation.fits
2025-07-21 10:36:20,400 - INFO - 使用整个图像进行分析: 300×300 像素
2025-07-21 10:36:20,400 - INFO - 图像加载成功: (300, 300)
2025-07-21 10:36:20,401 - INFO - 步骤2: 图像预处理
2025-07-21 10:36:20,403 - INFO - 图像预处理完成
2025-07-21 10:36:20,404 - INFO - 图像预处理完成
2025-07-21 10:36:20,404 - INFO - 步骤3: 特征检测和匹配
2025-07-21 10:36:20,740 - INFO - 检测到特征点: 图像1=229, 图像2=14
2025-07-21 10:36:20,746 - INFO - 找到 8 个匹配点
2025-07-21 10:36:20,746 - INFO - 匹配质量分析:
2025-07-21 10:36:20,746 - INFO -   总匹配数: 8
2025-07-21 10:36:20,747 - INFO -   平均距离: 82.50
2025-07-21 10:36:20,747 - INFO -   最小距离: 75.00
2025-07-21 10:36:20,747 - INFO -   最大距离: 87.00
2025-07-21 10:36:20,748 - INFO -   距离标准差: 4.18
2025-07-21 10:36:20,748 - INFO - 匹配点空间分布:
2025-07-21 10:36:20,748 - INFO -   图像1分布范围: X=170.6, Y=215.4
2025-07-21 10:36:20,748 - INFO -   图像2分布范围: X=51.2, Y=99.6
2025-07-21 10:36:20,748 - WARNING - ⚠️  匹配点数量较少，建议调整ORB参数
2025-07-21 10:36:20,748 - WARNING - ⚠️  平均匹配距离较大，匹配质量可能不佳
2025-07-21 10:36:20,748 - INFO - 步骤4: 图像对齐
2025-07-21 10:36:20,749 - INFO - 使用刚体变换（仅平移和旋转）
2025-07-21 10:36:20,750 - INFO - 变换分析:
2025-07-21 10:36:20,750 - INFO -   平移: (261.10, -210.37) 像素
2025-07-21 10:36:20,750 - INFO -   旋转: -49.72 度
2025-07-21 10:36:20,750 - INFO -   缩放: X=1.8686, Y=1.8686
2025-07-21 10:36:20,750 - INFO -   变换类型: 相似变换（平移、旋转和等比缩放）
2025-07-21 10:36:20,750 - INFO - 图像对齐完成（使用天文图像友好的变换）
2025-07-21 10:36:20,751 - INFO - 已将变换应用到原始图像
2025-07-21 10:36:20,751 - INFO - 步骤5: 差异检测
2025-07-21 10:36:20,755 - INFO - 检测到 172 个新亮点
2025-07-21 10:36:20,755 - INFO - 步骤6: 标记新亮点
2025-07-21 10:36:20,761 - INFO - 标记了 172 个新亮点
2025-07-21 10:36:20,761 - INFO - 步骤8: 保存结果
2025-07-21 10:36:20,775 - INFO - 已保存参考图像FITS文件: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uoimg1bh\results\comparison_reference_template_20250721_103620.fits
2025-07-21 10:36:20,778 - INFO - 已保存对齐后图像FITS文件: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uoimg1bh\results\comparison_aligned_observation_20250721_103620.fits
2025-07-21 10:36:20,779 - INFO - 所有结果已保存到目录: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uoimg1bh\results
2025-07-21 10:36:20,780 - INFO - ============================================================
2025-07-21 10:36:20,780 - INFO - FITS图像对齐和差异检测完成
2025-07-21 10:36:20,780 - INFO - 检测到 172 个新亮点
2025-07-21 10:36:20,780 - INFO - ============================================================
2025-07-21 10:36:20,781 - INFO - 步骤2: 执行已对齐文件差异比较...
2025-07-21 10:36:20,781 - INFO - 参考文件: comparison_reference_template_20250721_103620.fits
2025-07-21 10:36:20,781 - INFO - 对齐文件: comparison_aligned_observation_20250721_103620.fits
2025-07-21 10:36:20,782 - INFO - 加载FITS文件...
2025-07-21 10:36:20,783 - INFO - 成功加载FITS文件: comparison_reference_template_20250721_103620.fits, 形状: (300, 300)
2025-07-21 10:36:20,784 - INFO - 成功加载FITS文件: comparison_aligned_observation_20250721_103620.fits, 形状: (300, 300)
2025-07-21 10:36:20,785 - INFO - 执行差异检测...
2025-07-21 10:36:20,786 - INFO - 重叠区域像素数: 89949, 总像素数: 90000, 重叠比例: 99.94%
2025-07-21 10:36:20,802 - INFO - 检测到 483 个新亮点
2025-07-21 10:36:20,813 - INFO - 应用重叠掩码，确保非重叠区域为黑色...
2025-07-21 10:36:20,814 - INFO - 保存FITS格式结果...
2025-07-21 10:36:20,817 - INFO - FITS文件已保存: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uoimg1bh\results\aligned_comparison_20250721_103620_difference.fits
2025-07-21 10:36:20,819 - INFO - FITS文件已保存: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uoimg1bh\results\aligned_comparison_20250721_103620_binary_diff.fits
2025-07-21 10:36:20,822 - INFO - FITS文件已保存: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uoimg1bh\results\aligned_comparison_20250721_103620_marked.fits
2025-07-21 10:36:20,824 - INFO - FITS文件已保存: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uoimg1bh\results\aligned_comparison_20250721_103620_overlap_mask.fits
2025-07-21 10:36:20,825 - INFO - 保存JPG格式结果...
2025-07-21 10:36:21,464 - INFO - JPG文件已保存: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uoimg1bh\results\aligned_comparison_20250721_103620_reference.jpg
2025-07-21 10:36:21,940 - INFO - JPG文件已保存: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uoimg1bh\results\aligned_comparison_20250721_103620_aligned.jpg
2025-07-21 10:36:22,479 - INFO - JPG文件已保存: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uoimg1bh\results\aligned_comparison_20250721_103620_difference.jpg
2025-07-21 10:36:23,263 - INFO - JPG文件已保存: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uoimg1bh\results\aligned_comparison_20250721_103620_binary_diff.jpg
2025-07-21 10:36:24,175 - INFO - JPG文件已保存: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uoimg1bh\results\aligned_comparison_20250721_103620_overlap_mask.jpg
2025-07-21 10:36:24,669 - INFO - diff操作成功完成
2025-07-21 10:36:24,669 - INFO -   对齐成功: False
2025-07-21 10:36:24,669 - INFO -   检测到新亮点: 483 个
2025-07-21 10:36:24,670 - INFO - 扫描输出目录: C:\Users\<USER>\AppData\Local\Temp\diff_output_test_uoimg1bh\results
2025-07-21 10:36:24,670 - INFO - 找到 19 个文件
2025-07-21 10:36:24,671 - INFO - 找到差异FITS文件: aligned_comparison_20250721_103620_binary_diff.fits
2025-07-21 10:36:24,671 - INFO - 找到差异JPG文件: aligned_comparison_20250721_103620_binary_diff.jpg
2025-07-21 10:36:24,671 - INFO - 找到报告文件: aligned_comparison_20250721_103620_bright_spots.txt
2025-07-21 10:36:24,672 - INFO - 找到差异FITS文件: aligned_comparison_20250721_103620_difference.fits
2025-07-21 10:36:24,672 - INFO - 找到差异JPG文件: aligned_comparison_20250721_103620_difference.jpg
2025-07-21 10:36:24,672 - INFO - 找到标记FITS文件: aligned_comparison_20250721_103620_marked.fits
2025-07-21 10:36:24,676 - INFO - 找到标记JPG文件: aligned_comparison_20250721_103620_marked.jpg
2025-07-21 10:36:24,677 - INFO - 找到对齐FITS文件: aligned_comparison_20250721_103620_overlap_mask.fits
2025-07-21 10:36:24,677 - INFO - 找到对齐FITS文件: comparison_aligned_observation_20250721_103620.fits
2025-07-21 10:36:24,678 - INFO - 找到差异PNG文件: comparison_binary_diff_20250721_103620.png
2025-07-21 10:36:24,678 - INFO - 找到报告文件: comparison_bright_spots_20250721_103620.txt
2025-07-21 10:36:24,678 - INFO - 找到差异PNG文件: comparison_difference_20250721_103620.png
2025-07-21 10:36:24,678 - INFO - 找到标记PNG文件: comparison_marked_20250721_103620.png
2025-07-21 10:36:24,679 - INFO - 找到参考FITS文件: comparison_reference_template_20250721_103620.fits
2025-07-21 10:36:24,679 - INFO - 收集到 9 个输出文件: ['difference_fits', 'difference_jpg', 'report_txt', 'marked_fits', 'marked_jpg', 'aligned_fits', 'difference_png', 'marked_png', 'reference_fits']
2025-07-21 10:36:24,739 - INFO - 扫描输出目录: C:\Users\<USER>\AppData\Local\Temp\file_collection_test_ksjhck_3
2025-07-21 10:36:24,740 - INFO - 找到 9 个文件
2025-07-21 10:36:24,740 - INFO - 找到对齐FITS文件: aligned_image.fits
2025-07-21 10:36:24,741 - INFO - 找到报告文件: bright_spots_report.txt
2025-07-21 10:36:24,741 - INFO - 找到标记FITS文件: output_marked.fits
2025-07-21 10:36:24,741 - INFO - 找到参考FITS文件: reference_template.fits
2025-07-21 10:36:24,742 - INFO - 找到差异FITS文件: result_diff.fits
2025-07-21 10:36:24,742 - INFO - 未分类的FITS文件: some_other_file.fits
2025-07-21 10:36:24,742 - INFO - 找到差异FITS文件: test_difference.fits
2025-07-21 10:36:24,742 - INFO - 收集到 5 个输出文件: ['aligned_fits', 'report_txt', 'marked_fits', 'reference_fits', 'difference_fits']
2025-07-21 11:01:31,123 - INFO - 首次打开图像查看器，自动刷新目录树
