# 暗场提取器 (Dark Field Extractor)

从星空数据中提取暗场信息的工具，用于天文图像的噪声分析和校准。

## 功能特点

- **智能星点检测**: 使用形态学方法检测和遮罩星点区域
- **多种背景估计方法**: 支持sigma裁剪、中位数、百分位数等方法
- **批量处理**: 支持单文件和批量目录处理
- **可视化输出**: 生成详细的分析可视化图像
- **结果保存**: 保存暗场FITS文件和处理报告
- **参数可调**: 灵活的参数配置适应不同数据

## 安装依赖

```bash
pip install numpy opencv-python astropy matplotlib
```

## 快速开始

### 1. 创建主暗场（推荐）

对于同一相机同一参数拍摄的多个FITS文件，推荐创建统一的主暗场：

```python
from dark_field_extractor import DarkFieldExtractor

# 创建提取器
extractor = DarkFieldExtractor(
    star_detection_threshold=3.0,  # 3σ阈值检测星点
    background_method='sigma_clipped'
)

# 创建主暗场
result = extractor.create_master_dark_from_directory(
    input_dir="E:/fix_data/test/GY5/20250722/K056",
    output_dir="master_dark_output",
    file_pattern="*.fit*"
)

print(f"主暗场水平: {result['master_dark_level']:.2f} ± {result['master_dark_std']:.2f}")
print(f"处理文件数: {result['processed_files']}/{result['total_files']}")
```

### 2. 处理单个FITS文件

```python
# 处理单个文件
result = extractor.process_single_fits(
    fits_path="your_fits_file.fit",
    output_dir="output_directory",
    save_visualization=True,
    save_dark_fits=True
)

print(f"暗场水平: {result['dark_level']:.2f}")
print(f"标准差: {result['dark_std']:.2f}")
```

### 3. 批量处理目录（单独处理每个文件）

```python
# 批量处理目录中的所有FITS文件（为每个文件单独生成暗场）
results = extractor.process_directory(
    input_dir="E:/fix_data/test/GY5/20250722/K056",
    output_dir="dark_analysis_output",
    file_pattern="*.fit*"
)

print(f"成功处理 {sum(1 for r in results if r['success'])}/{len(results)} 个文件")
```

### 4. 命令行使用

```bash
# 创建主暗场（推荐）
python create_master_dark.py "E:/fix_data/test/GY5/20250722/K056" -o master_dark_output

# 使用原始工具处理单个文件
python dark_field_extractor.py input_file.fit -o output_dir

# 使用原始工具批量处理目录
python dark_field_extractor.py input_directory -o output_dir -p "*.fit*"

# 使用原始工具创建主暗场
python dark_field_extractor.py input_directory -o output_dir --master-dark

# 自定义参数
python create_master_dark.py input_directory \
    --threshold 2.5 \
    --method median \
    --expand-radius 3
```

## 参数说明

### 核心参数

- `star_detection_threshold`: 星点检测阈值（sigma倍数），默认3.0
- `min_star_area`: 最小星点面积（像素），默认5
- `max_star_area`: 最大星点面积（像素），默认1000
- `mask_expansion_radius`: 星点遮罩扩展半径，默认5
- `background_method`: 背景估计方法，可选：
  - `sigma_clipped`: sigma裁剪统计（推荐）
  - `median`: 中位数方法
  - `percentile`: 百分位数方法

### 高级参数

- `sigma_clip_sigma`: sigma裁剪的sigma值，默认3.0
- `sigma_clip_maxiters`: sigma裁剪最大迭代次数，默认5

## 输出文件

### 主暗场模式输出

#### 1. 主暗场FITS文件
- 文件名: `master_dark.fits`
- 内容: 从所有文件合并估计的主暗场图像数据
- 头信息: 包含处理参数、统计信息和源文件列表

#### 2. 主暗场可视化图像
- 文件名: `master_dark_analysis.jpg`
- 内容: 四个子图显示主暗场分析过程
  - 合并背景像素分布直方图
  - 各文件暗场水平对比
  - 各文件标准差对比
  - 统计信息汇总

#### 3. 主暗场报告
- 文件名: `master_dark_report.txt`
- 内容: 主暗场处理的详细统计和文件信息

### 单文件模式输出

#### 1. 暗场FITS文件
- 文件名: `{原文件名}_dark.fits`
- 内容: 估计的暗场图像数据
- 头信息: 包含处理参数和统计信息

#### 2. 可视化图像
- 文件名: `{原文件名}_dark_analysis.jpg`
- 内容: 四个子图显示分析过程
  - 原始图像
  - 星点检测结果
  - 扩展遮罩
  - 背景像素分布直方图

#### 3. 汇总报告
- 文件名: `dark_field_summary.txt`
- 内容: 批量处理的统计汇总和详细结果

## 使用示例

### 测试脚本
```bash
# 运行测试脚本
python test_dark_extractor.py

# 运行使用示例
python example_usage.py
```

### 处理测试数据
```python
# 使用提供的测试数据
test_dir = r"E:\fix_data\test\GY5\20250722\K056"
extractor = DarkFieldExtractor()
results = extractor.process_directory(test_dir)
```

## 算法原理

### 1. 星点检测
1. 使用sigma裁剪统计计算图像背景和噪声水平
2. 设置检测阈值为 `median + threshold * std`
3. 创建二值遮罩标记超过阈值的像素
4. 使用形态学操作去除噪声和小的伪检测
5. 根据面积过滤有效星点

### 2. 遮罩扩展
- 使用椭圆形核对星点遮罩进行膨胀操作
- 确保完全遮罩星点的光晕和周围影响区域

### 3. 暗场估计
- 从非星点区域（背景区域）的像素中估计暗场水平
- 支持多种统计方法以适应不同的噪声特性

## 注意事项

1. **数据格式**: 支持标准FITS格式，自动查找2D图像数据
2. **内存使用**: 大图像可能需要较多内存，建议监控内存使用
3. **参数调优**: 根据具体数据特点调整检测阈值和遮罩参数
4. **处理时间**: 批量处理大量文件可能需要较长时间

## 故障排除

### 常见问题

1. **检测不到星点**
   - 降低 `star_detection_threshold` 参数
   - 检查图像是否包含明显的星点

2. **背景估计不准确**
   - 尝试不同的 `background_method`
   - 调整星点检测参数以获得更好的遮罩

3. **处理速度慢**
   - 减少可视化输出 (`save_visualization=False`)
   - 使用更简单的背景估计方法

### 日志信息
程序会输出详细的日志信息，包括：
- 文件加载状态
- 星点检测结果
- 背景估计统计
- 处理进度和结果

## 扩展功能

可以基于此工具进一步开发：
- 时间序列暗场分析
- 温度相关的暗场建模
- 与其他校准流程的集成
- 自动化的质量评估
