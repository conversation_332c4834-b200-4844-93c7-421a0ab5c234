"""
创建主暗场的命令行工具
从多个同参数拍摄的FITS文件中创建统一的主暗场
"""

import sys
import argparse
from pathlib import Path
import logging

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from dark_field_extractor import DarkFieldExtractor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """
    主函数 - 命令行接口
    """
    parser = argparse.ArgumentParser(description='从星空数据中创建主暗场')
    parser.add_argument('input_dir', help='输入FITS文件目录路径')
    parser.add_argument('-o', '--output', help='输出目录路径，默认为输入目录下的master_dark子目录')
    parser.add_argument('-p', '--pattern', default='*.fit*', help='文件匹配模式 (默认: *.fit*)')
    parser.add_argument('--threshold', type=float, default=3.0, help='星点检测阈值 (默认: 3.0)')
    parser.add_argument('--min-area', type=int, default=5, help='最小星点面积 (默认: 5)')
    parser.add_argument('--max-area', type=int, default=1000, help='最大星点面积 (默认: 1000)')
    parser.add_argument('--expand-radius', type=int, default=5, help='遮罩扩展半径 (默认: 5)')
    parser.add_argument('--method', choices=['sigma_clipped', 'median', 'percentile'], 
                       default='sigma_clipped', help='背景估计方法 (默认: sigma_clipped)')
    parser.add_argument('--no-visualization', action='store_true', help='不保存可视化图像')
    
    args = parser.parse_args()
    
    # 检查输入目录
    input_path = Path(args.input_dir)
    if not input_path.exists():
        logger.error(f"输入目录不存在: {input_path}")
        return False
    
    if not input_path.is_dir():
        logger.error(f"输入路径不是目录: {input_path}")
        return False
    
    # 创建暗场提取器
    extractor = DarkFieldExtractor(
        star_detection_threshold=args.threshold,
        min_star_area=args.min_area,
        max_star_area=args.max_area,
        mask_expansion_radius=args.expand_radius,
        background_method=args.method
    )
    
    logger.info("开始创建主暗场")
    logger.info(f"输入目录: {input_path}")
    logger.info(f"文件模式: {args.pattern}")
    logger.info(f"星点检测阈值: {args.threshold}σ")
    logger.info(f"背景估计方法: {args.method}")
    
    try:
        # 创建主暗场
        result = extractor.create_master_dark_from_directory(
            input_dir=str(input_path),
            output_dir=args.output,
            file_pattern=args.pattern,
            save_visualization=not args.no_visualization
        )
        
        if result['success']:
            logger.info("主暗场创建成功!")
            logger.info(f"处理文件数: {result['processed_files']}/{result['total_files']}")
            logger.info(f"主暗场水平: {result['master_dark_level']:.2f} ± {result['master_dark_std']:.2f}")
            logger.info(f"总背景像素数: {result['total_background_pixels']:,}")
            
            # 显示各文件的暗场水平统计
            import numpy as np
            dark_levels = result['individual_dark_levels']
            if dark_levels:
                logger.info(f"各文件暗场水平统计:")
                logger.info(f"  均值: {np.mean(dark_levels):.2f}")
                logger.info(f"  中位数: {np.median(dark_levels):.2f}")
                logger.info(f"  标准差: {np.std(dark_levels):.2f}")
                logger.info(f"  范围: [{np.min(dark_levels):.2f}, {np.max(dark_levels):.2f}]")
            
            logger.info(f"输出文件:")
            logger.info(f"  主暗场FITS: {result['master_dark_fits']}")
            if result['visualization']:
                logger.info(f"  可视化图像: {result['visualization']}")
            
            return True
        else:
            logger.error(f"主暗场创建失败: {result['error']}")
            return False
            
    except Exception as e:
        logger.error(f"创建主暗场时出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✓ 主暗场创建完成!")
    else:
        print("\n✗ 主暗场创建失败!")
    sys.exit(0 if success else 1)
