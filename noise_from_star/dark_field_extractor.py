"""
从星空数据中提取暗场信息的工具
使用多种方法从包含星点的天文图像中估计暗场噪声
"""

import numpy as np
import cv2
from astropy.io import fits
from astropy.stats import sigma_clipped_stats
import matplotlib.pyplot as plt
import os
from pathlib import Path
import logging
from typing import List, Tuple, Dict, Optional
import glob
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DarkFieldExtractor:
    def __init__(self, 
                 star_detection_threshold: float = 3.0,
                 min_star_area: int = 5,
                 max_star_area: int = 1000,
                 mask_expansion_radius: int = 5,
                 background_method: str = 'sigma_clipped',
                 sigma_clip_sigma: float = 3.0,
                 sigma_clip_maxiters: int = 5):
        """
        初始化暗场提取器
        
        Args:
            star_detection_threshold: 星点检测阈值（sigma倍数）
            min_star_area: 最小星点面积
            max_star_area: 最大星点面积
            mask_expansion_radius: 星点遮罩扩展半径
            background_method: 背景估计方法 ('sigma_clipped', 'median', 'percentile')
            sigma_clip_sigma: sigma裁剪的sigma值
            sigma_clip_maxiters: sigma裁剪的最大迭代次数
        """
        self.star_detection_threshold = star_detection_threshold
        self.min_star_area = min_star_area
        self.max_star_area = max_star_area
        self.mask_expansion_radius = mask_expansion_radius
        self.background_method = background_method
        self.sigma_clip_sigma = sigma_clip_sigma
        self.sigma_clip_maxiters = sigma_clip_maxiters
        
        logger.info(f"暗场提取器初始化完成")
        logger.info(f"星点检测阈值: {star_detection_threshold}σ")
        logger.info(f"背景估计方法: {background_method}")
    
    def load_fits_image(self, fits_path: str) -> Tuple[Optional[np.ndarray], Optional[dict]]:
        """
        加载FITS图像
        
        Args:
            fits_path: FITS文件路径
            
        Returns:
            tuple: (图像数据, header信息)，失败时返回(None, None)
        """
        try:
            with fits.open(fits_path) as hdul:
                # 查找有效的2D图像数据
                data = None
                header = None
                
                for i, hdu in enumerate(hdul):
                    if hdu.data is not None and len(hdu.data.shape) == 2:
                        data = hdu.data.astype(np.float64)
                        header = hdu.header
                        logger.debug(f"使用HDU {i}, 数据形状: {data.shape}")
                        break
                
                if data is None:
                    logger.error(f"未找到有效的2D图像数据: {fits_path}")
                    return None, None
                
                logger.info(f"成功加载FITS文件: {Path(fits_path).name}")
                logger.info(f"数据形状: {data.shape}, 数据范围: [{np.min(data):.2f}, {np.max(data):.2f}]")
                
                return data, header
                
        except Exception as e:
            logger.error(f"加载FITS文件失败 {fits_path}: {str(e)}")
            return None, None
    
    def detect_stars_simple(self, image_data: np.ndarray) -> np.ndarray:
        """
        使用简单阈值方法检测星点
        
        Args:
            image_data: 图像数据
            
        Returns:
            星点二值遮罩
        """
        try:
            # 计算图像统计信息
            mean, median, std = sigma_clipped_stats(image_data, 
                                                  sigma=self.sigma_clip_sigma, 
                                                  maxiters=self.sigma_clip_maxiters)
            
            # 计算检测阈值
            threshold = median + self.star_detection_threshold * std
            
            # 创建二值遮罩
            star_mask = image_data > threshold
            
            logger.info(f"星点检测 - 均值: {mean:.2f}, 中位数: {median:.2f}, 标准差: {std:.2f}")
            logger.info(f"检测阈值: {threshold:.2f}, 检测到像素数: {np.sum(star_mask)}")
            
            return star_mask
            
        except Exception as e:
            logger.error(f"星点检测失败: {str(e)}")
            return np.zeros_like(image_data, dtype=bool)
    
    def detect_stars_morphology(self, image_data: np.ndarray) -> np.ndarray:
        """
        使用形态学方法检测星点
        
        Args:
            image_data: 图像数据
            
        Returns:
            星点二值遮罩
        """
        try:
            # 首先进行简单阈值检测
            star_mask = self.detect_stars_simple(image_data)
            
            # 转换为uint8格式进行形态学操作
            mask_uint8 = (star_mask * 255).astype(np.uint8)
            
            # 形态学操作去除噪声
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            mask_uint8 = cv2.morphologyEx(mask_uint8, cv2.MORPH_OPEN, kernel)
            
            # 查找轮廓并过滤
            contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 创建新的遮罩
            filtered_mask = np.zeros_like(image_data, dtype=bool)
            valid_stars = 0
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if self.min_star_area <= area <= self.max_star_area:
                    # 填充轮廓
                    cv2.fillPoly(mask_uint8, [contour], 255)
                    valid_stars += 1
            
            filtered_mask = mask_uint8 > 0
            
            logger.info(f"形态学星点检测 - 有效星点数: {valid_stars}, 总像素数: {np.sum(filtered_mask)}")
            
            return filtered_mask
            
        except Exception as e:
            logger.error(f"形态学星点检测失败: {str(e)}")
            return self.detect_stars_simple(image_data)
    
    def expand_star_mask(self, star_mask: np.ndarray) -> np.ndarray:
        """
        扩展星点遮罩以包含更多周围区域
        
        Args:
            star_mask: 原始星点遮罩
            
        Returns:
            扩展后的遮罩
        """
        if self.mask_expansion_radius <= 0:
            return star_mask
        
        try:
            # 创建扩展核
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, 
                                             (2 * self.mask_expansion_radius + 1, 
                                              2 * self.mask_expansion_radius + 1))
            
            # 转换为uint8格式
            mask_uint8 = (star_mask * 255).astype(np.uint8)
            
            # 执行膨胀操作
            expanded_mask = cv2.dilate(mask_uint8, kernel, iterations=1)
            
            expanded_mask = expanded_mask > 0
            
            logger.info(f"遮罩扩展 - 原始像素数: {np.sum(star_mask)}, 扩展后: {np.sum(expanded_mask)}")
            
            return expanded_mask
            
        except Exception as e:
            logger.error(f"遮罩扩展失败: {str(e)}")
            return star_mask
    
    def estimate_dark_field(self, image_data: np.ndarray, star_mask: np.ndarray) -> Dict:
        """
        从图像中估计暗场信息
        
        Args:
            image_data: 图像数据
            star_mask: 星点遮罩（True表示星点区域）
            
        Returns:
            包含暗场估计结果的字典
        """
        try:
            # 创建背景区域遮罩（非星点区域）
            background_mask = ~star_mask
            background_pixels = image_data[background_mask]
            
            if len(background_pixels) == 0:
                logger.warning("没有有效的背景像素")
                return {
                    'dark_level': np.nan,
                    'dark_std': np.nan,
                    'dark_map': np.full_like(image_data, np.nan),
                    'method': self.background_method,
                    'background_pixels': 0
                }
            
            # 根据方法估计暗场
            if self.background_method == 'sigma_clipped':
                mean, median, std = sigma_clipped_stats(background_pixels, 
                                                      sigma=self.sigma_clip_sigma,
                                                      maxiters=self.sigma_clip_maxiters)
                dark_level = median
                dark_std = std
                
            elif self.background_method == 'median':
                dark_level = np.median(background_pixels)
                dark_std = np.std(background_pixels)
                
            elif self.background_method == 'percentile':
                dark_level = np.percentile(background_pixels, 50)  # 中位数
                dark_std = np.percentile(background_pixels, 84) - np.percentile(background_pixels, 16)
                dark_std /= 2  # 近似标准差
                
            else:
                logger.warning(f"未知的背景估计方法: {self.background_method}, 使用中位数")
                dark_level = np.median(background_pixels)
                dark_std = np.std(background_pixels)
            
            # 创建暗场图
            dark_map = np.full_like(image_data, dark_level)
            
            result = {
                'dark_level': float(dark_level),
                'dark_std': float(dark_std),
                'dark_map': dark_map,
                'method': self.background_method,
                'background_pixels': len(background_pixels),
                'total_pixels': image_data.size,
                'star_pixels': np.sum(star_mask),
                'background_fraction': len(background_pixels) / image_data.size
            }
            
            logger.info(f"暗场估计完成 - 水平: {dark_level:.2f}, 标准差: {dark_std:.2f}")
            logger.info(f"背景像素数: {len(background_pixels)} ({result['background_fraction']:.1%})")
            
            return result
            
        except Exception as e:
            logger.error(f"暗场估计失败: {str(e)}")
            return {
                'dark_level': np.nan,
                'dark_std': np.nan,
                'dark_map': np.full_like(image_data, np.nan),
                'method': self.background_method,
                'background_pixels': 0
            }

    def process_single_fits(self, fits_path: str, output_dir: str = None,
                           save_visualization: bool = True,
                           save_dark_fits: bool = True) -> Dict:
        """
        处理单个FITS文件

        Args:
            fits_path: FITS文件路径
            output_dir: 输出目录，默认为输入文件所在目录
            save_visualization: 是否保存可视化图像
            save_dark_fits: 是否保存暗场FITS文件

        Returns:
            处理结果字典
        """
        try:
            fits_path = Path(fits_path)
            if output_dir is None:
                output_dir = fits_path.parent
            else:
                output_dir = Path(output_dir)
                output_dir.mkdir(parents=True, exist_ok=True)

            logger.info(f"开始处理文件: {fits_path.name}")

            # 加载图像
            image_data, header = self.load_fits_image(str(fits_path))
            if image_data is None:
                return {'success': False, 'error': '无法加载FITS文件'}

            # 检测星点
            star_mask = self.detect_stars_morphology(image_data)

            # 扩展星点遮罩
            expanded_mask = self.expand_star_mask(star_mask)

            # 估计暗场
            dark_result = self.estimate_dark_field(image_data, expanded_mask)

            # 生成输出文件名
            base_name = fits_path.stem

            # 保存暗场FITS文件
            dark_fits_path = None
            if save_dark_fits:
                dark_fits_path = output_dir / f"{base_name}_dark.fits"
                self.save_dark_fits(dark_result['dark_map'], header, str(dark_fits_path))

            # 保存可视化图像
            visualization_path = None
            if save_visualization:
                visualization_path = output_dir / f"{base_name}_dark_analysis.jpg"
                self.create_visualization(image_data, star_mask, expanded_mask,
                                        dark_result, str(visualization_path))

            # 组装结果
            result = {
                'success': True,
                'input_file': str(fits_path),
                'dark_fits_output': str(dark_fits_path) if dark_fits_path else None,
                'visualization_output': str(visualization_path) if visualization_path else None,
                'dark_level': dark_result['dark_level'],
                'dark_std': dark_result['dark_std'],
                'background_pixels': dark_result['background_pixels'],
                'star_pixels': dark_result['star_pixels'],
                'background_fraction': dark_result['background_fraction'],
                'method': dark_result['method']
            }

            logger.info(f"文件处理完成: {fits_path.name}")
            return result

        except Exception as e:
            logger.error(f"处理文件失败 {fits_path}: {str(e)}")
            return {'success': False, 'error': str(e)}

    def save_dark_fits(self, dark_map: np.ndarray, original_header: dict, output_path: str):
        """
        保存暗场FITS文件

        Args:
            dark_map: 暗场图像数据
            original_header: 原始FITS头信息
            output_path: 输出文件路径
        """
        try:
            # 复制原始头信息
            header = original_header.copy() if original_header else fits.Header()

            # 添加处理信息
            header['HISTORY'] = f'Dark field extracted by DarkFieldExtractor'
            header['HISTORY'] = f'Processing time: {datetime.now().isoformat()}'
            header['DARKLEVL'] = (float(np.mean(dark_map)), 'Estimated dark level')
            header['DARKSTD'] = (float(np.std(dark_map)), 'Dark field standard deviation')
            header['METHOD'] = (self.background_method, 'Background estimation method')

            # 创建FITS文件
            hdu = fits.PrimaryHDU(data=dark_map.astype(np.float32), header=header)
            hdu.writeto(output_path, overwrite=True)

            logger.info(f"暗场FITS文件已保存: {output_path}")

        except Exception as e:
            logger.error(f"保存暗场FITS文件失败: {str(e)}")

    def create_visualization(self, image_data: np.ndarray, star_mask: np.ndarray,
                           expanded_mask: np.ndarray, dark_result: Dict, output_path: str):
        """
        创建分析可视化图像

        Args:
            image_data: 原始图像数据
            star_mask: 星点遮罩
            expanded_mask: 扩展后的遮罩
            dark_result: 暗场估计结果
            output_path: 输出文件路径
        """
        try:
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))
            fig.suptitle('暗场提取分析', fontsize=16)

            # 原始图像
            im1 = axes[0, 0].imshow(image_data, cmap='gray', origin='lower')
            axes[0, 0].set_title('原始图像')
            axes[0, 0].set_xlabel('X像素')
            axes[0, 0].set_ylabel('Y像素')
            plt.colorbar(im1, ax=axes[0, 0])

            # 星点遮罩
            axes[0, 1].imshow(star_mask, cmap='Reds', origin='lower', alpha=0.7)
            axes[0, 1].imshow(image_data, cmap='gray', origin='lower', alpha=0.3)
            axes[0, 1].set_title(f'星点检测 ({np.sum(star_mask)} 像素)')
            axes[0, 1].set_xlabel('X像素')
            axes[0, 1].set_ylabel('Y像素')

            # 扩展遮罩
            axes[1, 0].imshow(expanded_mask, cmap='Blues', origin='lower', alpha=0.7)
            axes[1, 0].imshow(image_data, cmap='gray', origin='lower', alpha=0.3)
            axes[1, 0].set_title(f'扩展遮罩 ({np.sum(expanded_mask)} 像素)')
            axes[1, 0].set_xlabel('X像素')
            axes[1, 0].set_ylabel('Y像素')

            # 背景像素直方图
            background_pixels = image_data[~expanded_mask]
            if len(background_pixels) > 0:
                axes[1, 1].hist(background_pixels, bins=50, alpha=0.7, density=True)
                axes[1, 1].axvline(dark_result['dark_level'], color='red', linestyle='--',
                                 label=f"暗场水平: {dark_result['dark_level']:.2f}")
                axes[1, 1].set_title('背景像素分布')
                axes[1, 1].set_xlabel('像素值')
                axes[1, 1].set_ylabel('密度')
                axes[1, 1].legend()

            # 添加统计信息
            stats_text = f"""统计信息:
暗场水平: {dark_result['dark_level']:.2f}
标准差: {dark_result['dark_std']:.2f}
背景像素: {dark_result['background_pixels']}
背景比例: {dark_result['background_fraction']:.1%}
方法: {dark_result['method']}"""

            fig.text(0.02, 0.02, stats_text, fontsize=10, verticalalignment='bottom',
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

            plt.tight_layout()
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            plt.close()

            logger.info(f"可视化图像已保存: {output_path}")

        except Exception as e:
            logger.error(f"创建可视化失败: {str(e)}")

    def process_directory(self, input_dir: str, output_dir: str = None,
                         file_pattern: str = "*.fit*",
                         save_visualization: bool = True,
                         save_dark_fits: bool = True) -> List[Dict]:
        """
        批量处理目录中的FITS文件

        Args:
            input_dir: 输入目录
            output_dir: 输出目录，默认为输入目录下的dark_analysis子目录
            file_pattern: 文件匹配模式
            save_visualization: 是否保存可视化图像
            save_dark_fits: 是否保存暗场FITS文件

        Returns:
            处理结果列表
        """
        try:
            input_dir = Path(input_dir)
            if not input_dir.exists():
                logger.error(f"输入目录不存在: {input_dir}")
                return []

            if output_dir is None:
                output_dir = input_dir / "dark_analysis"
            else:
                output_dir = Path(output_dir)

            output_dir.mkdir(parents=True, exist_ok=True)

            # 查找FITS文件
            fits_files = list(input_dir.glob(file_pattern))
            if not fits_files:
                logger.warning(f"在目录 {input_dir} 中未找到匹配 {file_pattern} 的文件")
                return []

            logger.info(f"找到 {len(fits_files)} 个FITS文件")

            results = []
            for i, fits_file in enumerate(fits_files, 1):
                logger.info(f"处理进度: {i}/{len(fits_files)}")
                result = self.process_single_fits(str(fits_file), str(output_dir),
                                                save_visualization, save_dark_fits)
                results.append(result)

            # 生成汇总报告
            self.generate_summary_report(results, str(output_dir))

            logger.info(f"批量处理完成，共处理 {len(fits_files)} 个文件")
            return results

        except Exception as e:
            logger.error(f"批量处理失败: {str(e)}")
            return []

    def generate_summary_report(self, results: List[Dict], output_dir: str):
        """
        生成处理结果汇总报告

        Args:
            results: 处理结果列表
            output_dir: 输出目录
        """
        try:
            report_path = Path(output_dir) / "dark_field_summary.txt"

            successful_results = [r for r in results if r.get('success', False)]
            failed_results = [r for r in results if not r.get('success', False)]

            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("暗场提取汇总报告\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总文件数: {len(results)}\n")
                f.write(f"成功处理: {len(successful_results)}\n")
                f.write(f"处理失败: {len(failed_results)}\n\n")

                if successful_results:
                    # 计算统计信息
                    dark_levels = [r['dark_level'] for r in successful_results if not np.isnan(r['dark_level'])]
                    dark_stds = [r['dark_std'] for r in successful_results if not np.isnan(r['dark_std'])]
                    bg_fractions = [r['background_fraction'] for r in successful_results]

                    f.write("统计汇总:\n")
                    f.write("-" * 30 + "\n")
                    if dark_levels:
                        f.write(f"暗场水平 - 均值: {np.mean(dark_levels):.2f}, "
                               f"中位数: {np.median(dark_levels):.2f}, "
                               f"标准差: {np.std(dark_levels):.2f}\n")
                        f.write(f"暗场标准差 - 均值: {np.mean(dark_stds):.2f}, "
                               f"中位数: {np.median(dark_stds):.2f}\n")
                    f.write(f"背景比例 - 均值: {np.mean(bg_fractions):.1%}, "
                           f"中位数: {np.median(bg_fractions):.1%}\n\n")

                    f.write("详细结果:\n")
                    f.write("-" * 30 + "\n")
                    for result in successful_results:
                        filename = Path(result['input_file']).name
                        f.write(f"文件: {filename}\n")
                        f.write(f"  暗场水平: {result['dark_level']:.2f}\n")
                        f.write(f"  标准差: {result['dark_std']:.2f}\n")
                        f.write(f"  背景比例: {result['background_fraction']:.1%}\n")
                        f.write(f"  方法: {result['method']}\n\n")

                if failed_results:
                    f.write("处理失败的文件:\n")
                    f.write("-" * 30 + "\n")
                    for result in failed_results:
                        filename = Path(result.get('input_file', '未知')).name if result.get('input_file') else '未知'
                        f.write(f"文件: {filename}\n")
                        f.write(f"  错误: {result.get('error', '未知错误')}\n\n")

            logger.info(f"汇总报告已保存: {report_path}")

        except Exception as e:
            logger.error(f"生成汇总报告失败: {str(e)}")


def main():
    """
    主函数 - 命令行接口
    """
    import argparse

    parser = argparse.ArgumentParser(description='从星空数据中提取暗场信息')
    parser.add_argument('input_path', help='输入FITS文件或目录路径')
    parser.add_argument('-o', '--output', help='输出目录路径')
    parser.add_argument('-p', '--pattern', default='*.fit*', help='文件匹配模式 (默认: *.fit*)')
    parser.add_argument('--threshold', type=float, default=3.0, help='星点检测阈值 (默认: 3.0)')
    parser.add_argument('--min-area', type=int, default=5, help='最小星点面积 (默认: 5)')
    parser.add_argument('--max-area', type=int, default=1000, help='最大星点面积 (默认: 1000)')
    parser.add_argument('--expand-radius', type=int, default=5, help='遮罩扩展半径 (默认: 5)')
    parser.add_argument('--method', choices=['sigma_clipped', 'median', 'percentile'],
                       default='sigma_clipped', help='背景估计方法 (默认: sigma_clipped)')
    parser.add_argument('--no-visualization', action='store_true', help='不保存可视化图像')
    parser.add_argument('--no-fits', action='store_true', help='不保存暗场FITS文件')

    args = parser.parse_args()

    # 创建暗场提取器
    extractor = DarkFieldExtractor(
        star_detection_threshold=args.threshold,
        min_star_area=args.min_area,
        max_star_area=args.max_area,
        mask_expansion_radius=args.expand_radius,
        background_method=args.method
    )

    input_path = Path(args.input_path)

    if input_path.is_file():
        # 处理单个文件
        logger.info("处理单个FITS文件")
        result = extractor.process_single_fits(
            str(input_path),
            args.output,
            save_visualization=not args.no_visualization,
            save_dark_fits=not args.no_fits
        )

        if result['success']:
            logger.info("处理完成!")
            logger.info(f"暗场水平: {result['dark_level']:.2f}")
            logger.info(f"标准差: {result['dark_std']:.2f}")
        else:
            logger.error(f"处理失败: {result['error']}")

    elif input_path.is_dir():
        # 处理目录
        logger.info("批量处理目录中的FITS文件")
        results = extractor.process_directory(
            str(input_path),
            args.output,
            args.pattern,
            save_visualization=not args.no_visualization,
            save_dark_fits=not args.no_fits
        )

        successful = sum(1 for r in results if r.get('success', False))
        logger.info(f"批量处理完成! 成功处理 {successful}/{len(results)} 个文件")

    else:
        logger.error(f"输入路径不存在: {input_path}")


if __name__ == "__main__":
    main()
