"""
从星空数据中提取暗场信息的工具
使用多种方法从包含星点的天文图像中估计暗场噪声
"""

import numpy as np
import cv2
from astropy.io import fits
from astropy.stats import sigma_clipped_stats
import matplotlib.pyplot as plt
import os
from pathlib import Path
import logging
from typing import List, Tuple, Dict, Optional
import glob
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DarkFieldExtractor:
    def __init__(self, 
                 star_detection_threshold: float = 3.0,
                 min_star_area: int = 5,
                 max_star_area: int = 1000,
                 mask_expansion_radius: int = 5,
                 background_method: str = 'sigma_clipped',
                 sigma_clip_sigma: float = 3.0,
                 sigma_clip_maxiters: int = 5):
        """
        初始化暗场提取器
        
        Args:
            star_detection_threshold: 星点检测阈值（sigma倍数）
            min_star_area: 最小星点面积
            max_star_area: 最大星点面积
            mask_expansion_radius: 星点遮罩扩展半径
            background_method: 背景估计方法 ('sigma_clipped', 'median', 'percentile')
            sigma_clip_sigma: sigma裁剪的sigma值
            sigma_clip_maxiters: sigma裁剪的最大迭代次数
        """
        self.star_detection_threshold = star_detection_threshold
        self.min_star_area = min_star_area
        self.max_star_area = max_star_area
        self.mask_expansion_radius = mask_expansion_radius
        self.background_method = background_method
        self.sigma_clip_sigma = sigma_clip_sigma
        self.sigma_clip_maxiters = sigma_clip_maxiters
        
        logger.info(f"暗场提取器初始化完成")
        logger.info(f"星点检测阈值: {star_detection_threshold}σ")
        logger.info(f"背景估计方法: {background_method}")
    
    def load_fits_image(self, fits_path: str) -> Tuple[Optional[np.ndarray], Optional[dict]]:
        """
        加载FITS图像
        
        Args:
            fits_path: FITS文件路径
            
        Returns:
            tuple: (图像数据, header信息)，失败时返回(None, None)
        """
        try:
            with fits.open(fits_path) as hdul:
                # 查找有效的2D图像数据
                data = None
                header = None
                
                for i, hdu in enumerate(hdul):
                    if hdu.data is not None and len(hdu.data.shape) == 2:
                        data = hdu.data.astype(np.float64)
                        header = hdu.header
                        logger.debug(f"使用HDU {i}, 数据形状: {data.shape}")
                        break
                
                if data is None:
                    logger.error(f"未找到有效的2D图像数据: {fits_path}")
                    return None, None
                
                logger.info(f"成功加载FITS文件: {Path(fits_path).name}")
                logger.info(f"数据形状: {data.shape}, 数据范围: [{np.min(data):.2f}, {np.max(data):.2f}]")
                
                return data, header
                
        except Exception as e:
            logger.error(f"加载FITS文件失败 {fits_path}: {str(e)}")
            return None, None
    
    def detect_stars_simple(self, image_data: np.ndarray) -> np.ndarray:
        """
        使用简单阈值方法检测星点
        
        Args:
            image_data: 图像数据
            
        Returns:
            星点二值遮罩
        """
        try:
            # 计算图像统计信息
            mean, median, std = sigma_clipped_stats(image_data, 
                                                  sigma=self.sigma_clip_sigma, 
                                                  maxiters=self.sigma_clip_maxiters)
            
            # 计算检测阈值
            threshold = median + self.star_detection_threshold * std
            
            # 创建二值遮罩
            star_mask = image_data > threshold
            
            logger.info(f"星点检测 - 均值: {mean:.2f}, 中位数: {median:.2f}, 标准差: {std:.2f}")
            logger.info(f"检测阈值: {threshold:.2f}, 检测到像素数: {np.sum(star_mask)}")
            
            return star_mask
            
        except Exception as e:
            logger.error(f"星点检测失败: {str(e)}")
            return np.zeros_like(image_data, dtype=bool)
    
    def detect_stars_morphology(self, image_data: np.ndarray) -> np.ndarray:
        """
        使用形态学方法检测星点
        
        Args:
            image_data: 图像数据
            
        Returns:
            星点二值遮罩
        """
        try:
            # 首先进行简单阈值检测
            star_mask = self.detect_stars_simple(image_data)
            
            # 转换为uint8格式进行形态学操作
            mask_uint8 = (star_mask * 255).astype(np.uint8)
            
            # 形态学操作去除噪声
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            mask_uint8 = cv2.morphologyEx(mask_uint8, cv2.MORPH_OPEN, kernel)
            
            # 查找轮廓并过滤
            contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 创建新的遮罩
            filtered_mask = np.zeros_like(image_data, dtype=bool)
            valid_stars = 0
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if self.min_star_area <= area <= self.max_star_area:
                    # 填充轮廓
                    cv2.fillPoly(mask_uint8, [contour], 255)
                    valid_stars += 1
            
            filtered_mask = mask_uint8 > 0
            
            logger.info(f"形态学星点检测 - 有效星点数: {valid_stars}, 总像素数: {np.sum(filtered_mask)}")
            
            return filtered_mask
            
        except Exception as e:
            logger.error(f"形态学星点检测失败: {str(e)}")
            return self.detect_stars_simple(image_data)
    
    def expand_star_mask(self, star_mask: np.ndarray) -> np.ndarray:
        """
        扩展星点遮罩以包含更多周围区域
        
        Args:
            star_mask: 原始星点遮罩
            
        Returns:
            扩展后的遮罩
        """
        if self.mask_expansion_radius <= 0:
            return star_mask
        
        try:
            # 创建扩展核
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, 
                                             (2 * self.mask_expansion_radius + 1, 
                                              2 * self.mask_expansion_radius + 1))
            
            # 转换为uint8格式
            mask_uint8 = (star_mask * 255).astype(np.uint8)
            
            # 执行膨胀操作
            expanded_mask = cv2.dilate(mask_uint8, kernel, iterations=1)
            
            expanded_mask = expanded_mask > 0
            
            logger.info(f"遮罩扩展 - 原始像素数: {np.sum(star_mask)}, 扩展后: {np.sum(expanded_mask)}")
            
            return expanded_mask
            
        except Exception as e:
            logger.error(f"遮罩扩展失败: {str(e)}")
            return star_mask
    
    def estimate_dark_field(self, image_data: np.ndarray, star_mask: np.ndarray) -> Dict:
        """
        从图像中估计暗场信息
        
        Args:
            image_data: 图像数据
            star_mask: 星点遮罩（True表示星点区域）
            
        Returns:
            包含暗场估计结果的字典
        """
        try:
            # 创建背景区域遮罩（非星点区域）
            background_mask = ~star_mask
            background_pixels = image_data[background_mask]
            
            if len(background_pixels) == 0:
                logger.warning("没有有效的背景像素")
                return {
                    'dark_level': np.nan,
                    'dark_std': np.nan,
                    'dark_map': np.full_like(image_data, np.nan),
                    'method': self.background_method,
                    'background_pixels': 0
                }
            
            # 根据方法估计暗场
            if self.background_method == 'sigma_clipped':
                mean, median, std = sigma_clipped_stats(background_pixels, 
                                                      sigma=self.sigma_clip_sigma,
                                                      maxiters=self.sigma_clip_maxiters)
                dark_level = median
                dark_std = std
                
            elif self.background_method == 'median':
                dark_level = np.median(background_pixels)
                dark_std = np.std(background_pixels)
                
            elif self.background_method == 'percentile':
                dark_level = np.percentile(background_pixels, 50)  # 中位数
                dark_std = np.percentile(background_pixels, 84) - np.percentile(background_pixels, 16)
                dark_std /= 2  # 近似标准差
                
            else:
                logger.warning(f"未知的背景估计方法: {self.background_method}, 使用中位数")
                dark_level = np.median(background_pixels)
                dark_std = np.std(background_pixels)
            
            # 创建暗场图
            dark_map = np.full_like(image_data, dark_level)
            
            result = {
                'dark_level': float(dark_level),
                'dark_std': float(dark_std),
                'dark_map': dark_map,
                'method': self.background_method,
                'background_pixels': len(background_pixels),
                'total_pixels': image_data.size,
                'star_pixels': np.sum(star_mask),
                'background_fraction': len(background_pixels) / image_data.size
            }
            
            logger.info(f"暗场估计完成 - 水平: {dark_level:.2f}, 标准差: {dark_std:.2f}")
            logger.info(f"背景像素数: {len(background_pixels)} ({result['background_fraction']:.1%})")
            
            return result
            
        except Exception as e:
            logger.error(f"暗场估计失败: {str(e)}")
            return {
                'dark_level': np.nan,
                'dark_std': np.nan,
                'dark_map': np.full_like(image_data, np.nan),
                'method': self.background_method,
                'background_pixels': 0
            }
