"""
暗场提取器使用示例
演示如何使用DarkFieldExtractor从星空数据中提取暗场信息
"""

import sys
from pathlib import Path
import logging

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from dark_field_extractor import DarkFieldExtractor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def example_single_file():
    """
    示例1: 处理单个FITS文件
    """
    logger.info("示例1: 处理单个FITS文件")
    logger.info("-" * 40)
    
    # 创建暗场提取器
    extractor = DarkFieldExtractor(
        star_detection_threshold=3.0,  # 3σ阈值检测星点
        min_star_area=5,              # 最小星点面积5像素
        max_star_area=1000,           # 最大星点面积1000像素
        mask_expansion_radius=5,       # 遮罩扩展半径5像素
        background_method='sigma_clipped'  # 使用sigma裁剪方法
    )
    
    # 指定输入文件（请根据实际情况修改路径）
    input_file = r"E:\fix_data\test\GY5\20250722\K056\GY5_K056-1_No%20Filter_60S_Bin2_UTC20250722_195848_-15C_.fit"
    output_dir = Path(__file__).parent / "example_output"
    
    # 处理文件
    result = extractor.process_single_fits(
        fits_path=input_file,
        output_dir=str(output_dir),
        save_visualization=True,
        save_dark_fits=True
    )
    
    if result['success']:
        logger.info("处理成功!")
        logger.info(f"暗场水平: {result['dark_level']:.2f}")
        logger.info(f"标准差: {result['dark_std']:.2f}")
        logger.info(f"背景像素比例: {result['background_fraction']:.1%}")
        logger.info(f"输出文件:")
        logger.info(f"  可视化: {result['visualization_output']}")
        logger.info(f"  暗场FITS: {result['dark_fits_output']}")
    else:
        logger.error(f"处理失败: {result['error']}")

def example_batch_processing():
    """
    示例2: 批量处理目录中的所有FITS文件
    """
    logger.info("\n示例2: 批量处理目录")
    logger.info("-" * 40)
    
    # 创建暗场提取器，使用不同的参数
    extractor = DarkFieldExtractor(
        star_detection_threshold=2.5,  # 更敏感的检测
        min_star_area=3,
        max_star_area=800,
        mask_expansion_radius=3,
        background_method='median'      # 使用中位数方法
    )
    
    # 指定输入目录
    input_dir = r"E:\fix_data\test\GY5\20250722\K056"
    output_dir = Path(__file__).parent / "batch_output"
    
    # 批量处理
    results = extractor.process_directory(
        input_dir=input_dir,
        output_dir=str(output_dir),
        file_pattern="*.fit*",
        save_visualization=True,
        save_dark_fits=True
    )
    
    # 统计结果
    successful = [r for r in results if r.get('success', False)]
    failed = [r for r in results if not r.get('success', False)]
    
    logger.info(f"批量处理完成:")
    logger.info(f"  总文件数: {len(results)}")
    logger.info(f"  成功处理: {len(successful)}")
    logger.info(f"  处理失败: {len(failed)}")
    
    if successful:
        import numpy as np
        dark_levels = [r['dark_level'] for r in successful if not np.isnan(r['dark_level'])]
        if dark_levels:
            logger.info(f"暗场水平统计:")
            logger.info(f"  均值: {np.mean(dark_levels):.2f}")
            logger.info(f"  中位数: {np.median(dark_levels):.2f}")
            logger.info(f"  标准差: {np.std(dark_levels):.2f}")

def example_different_methods():
    """
    示例3: 比较不同的背景估计方法
    """
    logger.info("\n示例3: 比较不同背景估计方法")
    logger.info("-" * 40)
    
    # 测试文件
    input_file = r"E:\fix_data\test\GY5\20250722\K056\GY5_K056-1_No%20Filter_60S_Bin2_UTC20250722_195848_-15C_.fit"
    
    methods = ['sigma_clipped', 'median', 'percentile']
    results = {}
    
    for method in methods:
        logger.info(f"测试方法: {method}")
        
        extractor = DarkFieldExtractor(
            star_detection_threshold=3.0,
            background_method=method
        )
        
        output_dir = Path(__file__).parent / f"method_comparison_{method}"
        
        result = extractor.process_single_fits(
            fits_path=input_file,
            output_dir=str(output_dir),
            save_visualization=True,
            save_dark_fits=False  # 只保存一个方法的FITS文件
        )
        
        if result['success']:
            results[method] = {
                'dark_level': result['dark_level'],
                'dark_std': result['dark_std'],
                'background_fraction': result['background_fraction']
            }
            logger.info(f"  暗场水平: {result['dark_level']:.2f}")
            logger.info(f"  标准差: {result['dark_std']:.2f}")
        else:
            logger.error(f"  方法 {method} 处理失败: {result['error']}")
    
    # 比较结果
    if len(results) > 1:
        logger.info("\n方法比较:")
        for method, data in results.items():
            logger.info(f"  {method:15s}: 暗场={data['dark_level']:6.2f}, 标准差={data['dark_std']:6.2f}")

def example_parameter_tuning():
    """
    示例4: 参数调优示例
    """
    logger.info("\n示例4: 参数调优")
    logger.info("-" * 40)
    
    input_file = r"E:\fix_data\test\GY5\20250722\K056\GY5_K056-1_No%20Filter_60S_Bin2_UTC20250722_195848_-15C_.fit"
    
    # 测试不同的星点检测阈值
    thresholds = [2.0, 2.5, 3.0, 3.5, 4.0]
    
    logger.info("测试不同的星点检测阈值:")
    for threshold in thresholds:
        extractor = DarkFieldExtractor(
            star_detection_threshold=threshold,
            background_method='sigma_clipped'
        )
        
        output_dir = Path(__file__).parent / f"threshold_test_{threshold}"
        
        result = extractor.process_single_fits(
            fits_path=input_file,
            output_dir=str(output_dir),
            save_visualization=False,  # 不保存可视化以节省时间
            save_dark_fits=False
        )
        
        if result['success']:
            logger.info(f"  阈值 {threshold}σ: 暗场={result['dark_level']:6.2f}, "
                       f"背景比例={result['background_fraction']:5.1%}")

def main():
    """
    运行所有示例
    """
    logger.info("=" * 60)
    logger.info("暗场提取器使用示例")
    logger.info("=" * 60)
    
    try:
        # 检查测试数据是否存在
        test_file = r"E:\fix_data\test\GY5\20250722\K056\GY5_K056-1_No%20Filter_60S_Bin2_UTC20250722_195848_-15C_.fit"
        if not Path(test_file).exists():
            logger.warning("测试数据文件不存在，请检查路径:")
            logger.warning(test_file)
            logger.info("请修改示例中的文件路径后重新运行")
            return
        
        # 运行示例
        example_single_file()
        example_batch_processing()
        example_different_methods()
        example_parameter_tuning()
        
        logger.info("\n" + "=" * 60)
        logger.info("所有示例运行完成!")
        logger.info("请查看生成的输出目录以查看结果")
        
    except Exception as e:
        logger.error(f"运行示例时出错: {str(e)}")

if __name__ == "__main__":
    main()
