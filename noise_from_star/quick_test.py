"""
快速测试暗场提取器的脚本
用于快速验证单个文件的处理结果
"""

import sys
from pathlib import Path
import logging

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from dark_field_extractor import DarkFieldExtractor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def quick_test():
    """
    快速测试单个文件
    """
    # 测试文件路径
    test_file = r"E:\fix_data\test\GY5\20250722\K056\GY5_K056-1_No%20Filter_60S_Bin2_UTC20250722_195848_-15C_.fit"
    
    # 检查文件是否存在
    if not Path(test_file).exists():
        logger.error(f"测试文件不存在: {test_file}")
        return False
    
    # 创建输出目录
    output_dir = Path(__file__).parent / "quick_test_output"
    output_dir.mkdir(exist_ok=True)
    
    logger.info("开始快速测试暗场提取器")
    logger.info(f"测试文件: {Path(test_file).name}")
    logger.info(f"输出目录: {output_dir}")
    
    # 创建暗场提取器实例
    extractor = DarkFieldExtractor(
        star_detection_threshold=3.0,
        min_star_area=5,
        max_star_area=1000,
        mask_expansion_radius=5,
        background_method='sigma_clipped'
    )
    
    try:
        # 处理文件
        result = extractor.process_single_fits(
            str(test_file),
            str(output_dir),
            save_visualization=True,
            save_dark_fits=True
        )
        
        if result['success']:
            logger.info("快速测试成功!")
            logger.info(f"暗场水平: {result['dark_level']:.2f}")
            logger.info(f"标准差: {result['dark_std']:.2f}")
            logger.info(f"背景比例: {result['background_fraction']:.1%}")
            logger.info(f"星点像素数: {result['star_pixels']}")
            logger.info(f"背景像素数: {result['background_pixels']}")
            logger.info(f"可视化文件: {result['visualization_output']}")
            logger.info(f"暗场FITS文件: {result['dark_fits_output']}")
            return True
        else:
            logger.error(f"快速测试失败: {result['error']}")
            return False
            
    except Exception as e:
        logger.error(f"快速测试过程中出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = quick_test()
    if success:
        print("\n✓ 快速测试通过!")
    else:
        print("\n✗ 快速测试失败!")
    sys.exit(0 if success else 1)
