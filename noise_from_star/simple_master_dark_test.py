"""
简化的主暗场测试脚本
只处理前3个文件来快速验证功能
"""

import sys
from pathlib import Path
import logging
import glob

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from dark_field_extractor import DarkFieldExtractor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def simple_master_dark_test():
    """
    简化的主暗场测试 - 只处理前3个文件
    """
    # 测试数据目录
    test_data_dir = r"E:\fix_data\test\GY5\20250722\K056"
    
    # 检查测试数据目录是否存在
    if not Path(test_data_dir).exists():
        logger.error(f"测试数据目录不存在: {test_data_dir}")
        return False
    
    # 创建输出目录
    output_dir = Path(__file__).parent / "simple_master_dark_output"
    output_dir.mkdir(exist_ok=True)
    
    logger.info("开始简化主暗场测试")
    logger.info(f"测试数据目录: {test_data_dir}")
    logger.info(f"输出目录: {output_dir}")
    
    # 创建暗场提取器实例
    extractor = DarkFieldExtractor(
        star_detection_threshold=3.0,
        min_star_area=5,
        max_star_area=1000,
        mask_expansion_radius=5,
        background_method='sigma_clipped'
    )
    
    try:
        # 手动处理前3个文件
        fits_files = list(Path(test_data_dir).glob("*.fit*"))[:3]  # 只取前3个文件
        logger.info(f"找到 {len(fits_files)} 个测试文件")
        
        all_background_pixels = []
        all_dark_levels = []
        all_dark_stds = []
        processed_files = []
        
        # 处理每个文件
        for i, fits_file in enumerate(fits_files, 1):
            logger.info(f"处理进度: {i}/{len(fits_files)} - {fits_file.name}")
            
            # 加载图像
            image_data, header = extractor.load_fits_image(str(fits_file))
            if image_data is None:
                logger.warning(f"跳过文件: {fits_file.name}")
                continue
            
            # 检测星点
            star_mask = extractor.detect_stars_morphology(image_data)
            
            # 扩展星点遮罩
            expanded_mask = extractor.expand_star_mask(star_mask)
            
            # 估计暗场
            dark_result = extractor.estimate_dark_field(image_data, expanded_mask)
            
            if dark_result['background_pixels'] > 0:
                # 提取背景像素（只取一部分以节省内存）
                background_pixels = image_data[~expanded_mask]
                sample_size = min(100000, len(background_pixels))  # 最多取10万个像素
                background_sample = background_pixels[:sample_size]
                
                all_background_pixels.extend(background_sample)
                all_dark_levels.append(dark_result['dark_level'])
                all_dark_stds.append(dark_result['dark_std'])
                processed_files.append(fits_file.name)
                
                logger.info(f"  暗场水平: {dark_result['dark_level']:.2f}, "
                          f"背景像素: {dark_result['background_pixels']}")
        
        if not all_background_pixels:
            logger.error("没有有效的背景像素数据")
            return False
        
        # 计算主暗场统计
        import numpy as np
        from astropy.stats import sigma_clipped_stats
        
        all_background_pixels = np.array(all_background_pixels)
        
        mean, median, std = sigma_clipped_stats(all_background_pixels, 
                                              sigma=3.0, maxiters=5)
        master_dark_level = median
        master_dark_std = std
        
        # 使用第一个文件的形状创建主暗场图像
        first_image, first_header = extractor.load_fits_image(str(fits_files[0]))
        master_dark_map = np.full_like(first_image, master_dark_level)
        
        # 保存主暗场FITS文件
        from astropy.io import fits
        from datetime import datetime
        
        master_dark_fits_path = output_dir / "master_dark.fits"
        
        # 复制原始头信息
        header = first_header.copy() if first_header else fits.Header()
        
        # 添加主暗场信息
        header['HISTORY'] = f'Master dark field created by DarkFieldExtractor'
        header['HISTORY'] = f'Processing time: {datetime.now().isoformat()}'
        header['HISTORY'] = f'Combined {len(processed_files)} files'
        header['MDARKLEVL'] = (float(master_dark_level), 'Master dark level')
        header['MDARKSTD'] = (float(master_dark_std), 'Master dark standard deviation')
        header['METHOD'] = ('sigma_clipped', 'Background estimation method')
        header['NFILES'] = (len(processed_files), 'Number of files combined')
        
        # 添加处理的文件信息
        for i, filename in enumerate(processed_files):
            header[f'FILE{i+1:02d}'] = (filename, f'Input file {i+1}')
        
        # 创建FITS文件
        hdu = fits.PrimaryHDU(data=master_dark_map.astype(np.float32), header=header)
        hdu.writeto(str(master_dark_fits_path), overwrite=True)
        
        # 生成简单报告
        report_path = output_dir / "simple_master_dark_report.txt"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("简化主暗场提取报告\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"处理文件数: {len(processed_files)}\n")
            f.write(f"背景估计方法: sigma_clipped\n\n")
            
            f.write("主暗场统计:\n")
            f.write("-" * 20 + "\n")
            f.write(f"主暗场水平: {master_dark_level:.2f}\n")
            f.write(f"主暗场标准差: {master_dark_std:.2f}\n\n")
            
            f.write("各文件暗场水平:\n")
            f.write("-" * 20 + "\n")
            for i, (filename, dark_level, dark_std) in enumerate(zip(processed_files, all_dark_levels, all_dark_stds), 1):
                f.write(f"{i}. {filename}\n")
                f.write(f"   暗场水平: {dark_level:.2f}, 标准差: {dark_std:.2f}\n")
            
            f.write(f"\n统计汇总:\n")
            f.write("-" * 20 + "\n")
            f.write(f"各文件暗场水平均值: {np.mean(all_dark_levels):.2f}\n")
            f.write(f"各文件暗场水平标准差: {np.std(all_dark_levels):.2f}\n")
            f.write(f"总背景像素样本数: {len(all_background_pixels):,}\n")
        
        logger.info("简化主暗场测试成功!")
        logger.info(f"处理文件数: {len(processed_files)}")
        logger.info(f"主暗场水平: {master_dark_level:.2f} ± {master_dark_std:.2f}")
        logger.info(f"各文件暗场水平统计:")
        logger.info(f"  均值: {np.mean(all_dark_levels):.2f}")
        logger.info(f"  标准差: {np.std(all_dark_levels):.2f}")
        logger.info(f"  范围: [{np.min(all_dark_levels):.2f}, {np.max(all_dark_levels):.2f}]")
        logger.info(f"输出文件:")
        logger.info(f"  主暗场FITS: {master_dark_fits_path}")
        logger.info(f"  报告: {report_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = simple_master_dark_test()
    if success:
        print("\n✓ 简化主暗场测试通过!")
    else:
        print("\n✗ 简化主暗场测试失败!")
    sys.exit(0 if success else 1)
