"""
测试暗场提取器的脚本
使用指定的测试数据目录进行测试
"""

import sys
import os
from pathlib import Path
import logging

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from dark_field_extractor import DarkFieldExtractor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_dark_extractor():
    """
    测试暗场提取器
    """
    # 测试数据目录
    test_data_dir = r"E:\fix_data\test\GY5\20250722\K056"
    
    # 检查测试数据目录是否存在
    if not Path(test_data_dir).exists():
        logger.error(f"测试数据目录不存在: {test_data_dir}")
        return False
    
    # 创建输出目录
    output_dir = Path(__file__).parent / "test_output"
    output_dir.mkdir(exist_ok=True)
    
    logger.info("开始测试暗场提取器")
    logger.info(f"测试数据目录: {test_data_dir}")
    logger.info(f"输出目录: {output_dir}")
    
    # 创建暗场提取器实例
    extractor = DarkFieldExtractor(
        star_detection_threshold=3.0,
        min_star_area=5,
        max_star_area=1000,
        mask_expansion_radius=5,
        background_method='sigma_clipped'
    )
    
    try:
        # 批量处理测试数据
        results = extractor.process_directory(
            input_dir=test_data_dir,
            output_dir=str(output_dir),
            file_pattern="*.fit*",
            save_visualization=True,
            save_dark_fits=True
        )
        
        if not results:
            logger.error("没有处理任何文件")
            return False
        
        # 统计结果
        successful = [r for r in results if r.get('success', False)]
        failed = [r for r in results if not r.get('success', False)]
        
        logger.info(f"测试完成!")
        logger.info(f"总文件数: {len(results)}")
        logger.info(f"成功处理: {len(successful)}")
        logger.info(f"处理失败: {len(failed)}")
        
        if successful:
            # 显示一些统计信息
            import numpy as np
            dark_levels = [r['dark_level'] for r in successful if not np.isnan(r['dark_level'])]
            if dark_levels:
                logger.info(f"暗场水平统计:")
                logger.info(f"  均值: {np.mean(dark_levels):.2f}")
                logger.info(f"  中位数: {np.median(dark_levels):.2f}")
                logger.info(f"  标准差: {np.std(dark_levels):.2f}")
                logger.info(f"  范围: [{np.min(dark_levels):.2f}, {np.max(dark_levels):.2f}]")
        
        if failed:
            logger.warning("处理失败的文件:")
            for result in failed:
                filename = Path(result.get('input_file', '未知')).name if result.get('input_file') else '未知'
                logger.warning(f"  {filename}: {result.get('error', '未知错误')}")
        
        return len(successful) > 0
        
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}")
        return False

def test_single_file():
    """
    测试处理单个文件
    """
    test_data_dir = r"E:\fix_data\test\GY5\20250722\K056"
    
    # 查找第一个FITS文件进行测试
    test_files = list(Path(test_data_dir).glob("*.fit*"))
    if not test_files:
        logger.error(f"在 {test_data_dir} 中未找到FITS文件")
        return False
    
    test_file = test_files[0]
    output_dir = Path(__file__).parent / "single_test_output"
    output_dir.mkdir(exist_ok=True)
    
    logger.info(f"测试单个文件: {test_file.name}")
    
    # 创建暗场提取器
    extractor = DarkFieldExtractor(
        star_detection_threshold=2.5,  # 使用更敏感的阈值
        min_star_area=3,
        max_star_area=500,
        mask_expansion_radius=3,
        background_method='sigma_clipped'
    )
    
    try:
        result = extractor.process_single_fits(
            str(test_file),
            str(output_dir),
            save_visualization=True,
            save_dark_fits=True
        )
        
        if result['success']:
            logger.info("单文件测试成功!")
            logger.info(f"暗场水平: {result['dark_level']:.2f}")
            logger.info(f"标准差: {result['dark_std']:.2f}")
            logger.info(f"背景比例: {result['background_fraction']:.1%}")
            logger.info(f"可视化文件: {result['visualization_output']}")
            logger.info(f"暗场FITS文件: {result['dark_fits_output']}")
            return True
        else:
            logger.error(f"单文件测试失败: {result['error']}")
            return False
            
    except Exception as e:
        logger.error(f"单文件测试过程中出错: {str(e)}")
        return False

def main():
    """
    主测试函数
    """
    logger.info("=" * 60)
    logger.info("暗场提取器测试程序")
    logger.info("=" * 60)
    
    # 测试单个文件
    logger.info("\n1. 测试单个文件处理...")
    single_success = test_single_file()
    
    # 测试批量处理
    logger.info("\n2. 测试批量处理...")
    batch_success = test_dark_extractor()
    
    # 总结
    logger.info("\n" + "=" * 60)
    logger.info("测试结果总结:")
    logger.info(f"单文件测试: {'成功' if single_success else '失败'}")
    logger.info(f"批量处理测试: {'成功' if batch_success else '失败'}")
    
    if single_success and batch_success:
        logger.info("所有测试通过! ✓")
        return True
    else:
        logger.error("部分测试失败! ✗")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
