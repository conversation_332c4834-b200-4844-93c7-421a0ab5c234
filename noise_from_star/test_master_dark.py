"""
测试主暗场创建功能的脚本
从多个FITS文件中创建统一的主暗场
"""

import sys
import os
from pathlib import Path
import logging

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from dark_field_extractor import DarkFieldExtractor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_master_dark():
    """
    测试主暗场创建功能
    """
    # 测试数据目录
    test_data_dir = r"E:\fix_data\test\GY5\20250722\K056"
    
    # 检查测试数据目录是否存在
    if not Path(test_data_dir).exists():
        logger.error(f"测试数据目录不存在: {test_data_dir}")
        return False
    
    # 创建输出目录
    output_dir = Path(__file__).parent / "master_dark_output"
    output_dir.mkdir(exist_ok=True)
    
    logger.info("开始测试主暗场创建功能")
    logger.info(f"测试数据目录: {test_data_dir}")
    logger.info(f"输出目录: {output_dir}")
    
    # 创建暗场提取器实例
    extractor = DarkFieldExtractor(
        star_detection_threshold=3.0,
        min_star_area=5,
        max_star_area=1000,
        mask_expansion_radius=5,
        background_method='sigma_clipped'
    )
    
    try:
        # 创建主暗场
        result = extractor.create_master_dark_from_directory(
            input_dir=test_data_dir,
            output_dir=str(output_dir),
            file_pattern="*.fit*",
            save_visualization=True
        )
        
        if result['success']:
            logger.info("主暗场创建成功!")
            logger.info(f"处理文件数: {result['processed_files']}/{result['total_files']}")
            logger.info(f"主暗场水平: {result['master_dark_level']:.2f}")
            logger.info(f"主暗场标准差: {result['master_dark_std']:.2f}")
            logger.info(f"总背景像素数: {result['total_background_pixels']:,}")
            logger.info(f"背景估计方法: {result['method']}")
            
            # 显示各文件的暗场水平统计
            import numpy as np
            dark_levels = result['individual_dark_levels']
            dark_stds = result['individual_dark_stds']
            
            logger.info("\n各文件暗场水平统计:")
            logger.info(f"  均值: {np.mean(dark_levels):.2f}")
            logger.info(f"  中位数: {np.median(dark_levels):.2f}")
            logger.info(f"  标准差: {np.std(dark_levels):.2f}")
            logger.info(f"  范围: [{np.min(dark_levels):.2f}, {np.max(dark_levels):.2f}]")
            
            logger.info("\n输出文件:")
            logger.info(f"  主暗场FITS: {result['master_dark_fits']}")
            logger.info(f"  可视化图像: {result['visualization']}")
            
            return True
        else:
            logger.error(f"主暗场创建失败: {result['error']}")
            return False
            
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}")
        return False

def test_command_line():
    """
    测试命令行接口
    """
    logger.info("\n测试命令行接口...")
    
    test_data_dir = r"E:\fix_data\test\GY5\20250722\K056"
    output_dir = Path(__file__).parent / "master_dark_cli_output"
    
    # 构建命令行参数
    cmd = f'python dark_field_extractor.py "{test_data_dir}" -o "{output_dir}" --master-dark --threshold 3.0 --method sigma_clipped'
    
    logger.info(f"执行命令: {cmd}")
    
    try:
        import subprocess
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=Path(__file__).parent)
        
        if result.returncode == 0:
            logger.info("命令行测试成功!")
            logger.info("输出:")
            for line in result.stdout.split('\n')[-10:]:  # 显示最后10行
                if line.strip():
                    logger.info(f"  {line}")
            return True
        else:
            logger.error("命令行测试失败!")
            logger.error(f"错误输出: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"命令行测试出错: {str(e)}")
        return False

def compare_methods():
    """
    比较不同背景估计方法的结果
    """
    logger.info("\n比较不同背景估计方法...")
    
    test_data_dir = r"E:\fix_data\test\GY5\20250722\K056"
    methods = ['sigma_clipped', 'median', 'percentile']
    results = {}
    
    for method in methods:
        logger.info(f"\n测试方法: {method}")
        
        output_dir = Path(__file__).parent / f"master_dark_{method}"
        output_dir.mkdir(exist_ok=True)
        
        extractor = DarkFieldExtractor(
            star_detection_threshold=3.0,
            background_method=method
        )
        
        try:
            result = extractor.create_master_dark_from_directory(
                input_dir=test_data_dir,
                output_dir=str(output_dir),
                file_pattern="*.fit*",
                save_visualization=True
            )
            
            if result['success']:
                results[method] = {
                    'master_dark_level': result['master_dark_level'],
                    'master_dark_std': result['master_dark_std'],
                    'processed_files': result['processed_files']
                }
                logger.info(f"  主暗场水平: {result['master_dark_level']:.2f}")
                logger.info(f"  标准差: {result['master_dark_std']:.2f}")
            else:
                logger.error(f"  方法 {method} 失败: {result['error']}")
                
        except Exception as e:
            logger.error(f"  方法 {method} 出错: {str(e)}")
    
    # 比较结果
    if len(results) > 1:
        logger.info("\n方法比较:")
        logger.info("-" * 50)
        for method, data in results.items():
            logger.info(f"{method:15s}: 暗场={data['master_dark_level']:7.2f}, "
                       f"标准差={data['master_dark_std']:6.2f}, "
                       f"文件数={data['processed_files']}")
    
    return len(results) > 0

def main():
    """
    主测试函数
    """
    logger.info("=" * 60)
    logger.info("主暗场创建功能测试程序")
    logger.info("=" * 60)
    
    # 检查测试数据
    test_data_dir = r"E:\fix_data\test\GY5\20250722\K056"
    if not Path(test_data_dir).exists():
        logger.error(f"测试数据目录不存在: {test_data_dir}")
        logger.info("请确保测试数据路径正确")
        return False
    
    # 测试主暗场创建
    logger.info("\n1. 测试主暗场创建功能...")
    master_dark_success = test_master_dark()
    
    # 测试命令行接口
    logger.info("\n2. 测试命令行接口...")
    cli_success = test_command_line()
    
    # 比较不同方法
    logger.info("\n3. 比较不同背景估计方法...")
    compare_success = compare_methods()
    
    # 总结
    logger.info("\n" + "=" * 60)
    logger.info("测试结果总结:")
    logger.info(f"主暗场创建: {'成功' if master_dark_success else '失败'}")
    logger.info(f"命令行接口: {'成功' if cli_success else '失败'}")
    logger.info(f"方法比较: {'成功' if compare_success else '失败'}")
    
    overall_success = master_dark_success and cli_success and compare_success
    if overall_success:
        logger.info("所有测试通过! ✓")
        logger.info("\n请查看生成的输出目录以查看结果:")
        logger.info("- master_dark_output/")
        logger.info("- master_dark_cli_output/")
        logger.info("- master_dark_*/")
    else:
        logger.error("部分测试失败! ✗")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
