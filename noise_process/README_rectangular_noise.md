# 矩形噪点清理工具

本目录包含专门用于处理FITS文件中9个像素以内矩形噪点的清理工具。这些噪点的特点是矩形排列且具有比较尖锐的边缘。

## 文件说明

### 主要工具文件

1. **`rectangular_noise_cleaner.py`** - 完整版矩形噪点清理工具
   - 使用连通组件分析和形态学特征检测
   - 支持矩形度和边缘锐度分析
   - 提供多种清理方法（自适应、中值、高斯、插值）
   - 功能最全面但处理速度较慢

2. **`fast_rectangular_noise_cleaner.py`** - 快速版矩形噪点清理工具
   - 使用滑动窗口方法直接检测矩形区域
   - 专门针对小型矩形噪点优化
   - 处理速度更快但功能相对简化

3. **`demo_rectangular_cleaner.py`** - 演示版矩形噪点清理工具 ⭐ **推荐使用**
   - 基于现有的`isolated_noise_cleaner.py`
   - 针对矩形噪点优化参数设置
   - 运行稳定，效果良好
   - 提供参数对比功能

### 测试文件

1. **`test_rectangular_noise_cleaner.py`** - 完整版工具测试脚本
2. **`test_fast_rectangular.py`** - 快速版工具测试脚本
3. **`test_rectangular_simple.py`** - 简化测试脚本

## 快速开始

### 推荐使用演示版工具

```bash
# 基本使用
python demo_rectangular_cleaner.py

# 参数对比测试
python demo_rectangular_cleaner.py --compare
```

### 使用完整版工具

```bash
# 基本使用
python rectangular_noise_cleaner.py --input noise2.fits

# 自定义参数
python rectangular_noise_cleaner.py --input noise2.fits --threshold 5.0 --max-size 6 --rectangularity 0.7
```

## 测试结果

使用`noise2.fits`文件进行测试，结果如下：

### 演示版工具测试结果

| 配置 | 检测噪点数 | 噪点比例 | 平均变化 | 噪点区域变化 |
|------|------------|----------|----------|--------------|
| 严格检测 | 0 | 0.000% | 0.000000 | 0.000000 |
| 中等检测 | 0 | 0.000% | 0.000000 | 0.000000 |
| 宽松检测 | 11,724 | 0.077% | 0.253824 | 331.089583 |

**结论**：宽松检测模式成功检测并清理了11,724个噪点，效果显著。

## 参数说明

### 关键参数

- **`zscore_threshold`**: Z-score阈值，控制噪点检测的严格程度
  - 值越大越严格，检测到的噪点越少
  - 推荐值：3.0-5.0

- **`isolation_radius`**: 孤立性检测半径
  - 用于判断噪点是否孤立
  - 推荐值：1-2

- **`min_neighbors`**: 最小邻居数量
  - 邻居数量少于此值的像素被认为是孤立噪点
  - 推荐值：0-1

- **`cleaning_method`**: 清理方法
  - `median`: 中值滤波（推荐）
  - `gaussian`: 高斯滤波
  - `adaptive`: 自适应清理

### 优化建议

针对不同类型的矩形噪点，推荐以下参数组合：

1. **小型尖锐噪点**（1-3像素）：
   ```python
   zscore_threshold = 4.0
   isolation_radius = 1
   min_neighbors = 0
   cleaning_method = 'median'
   ```

2. **中等矩形噪点**（4-6像素）：
   ```python
   zscore_threshold = 3.5
   isolation_radius = 2
   min_neighbors = 1
   cleaning_method = 'adaptive'
   ```

3. **较大矩形噪点**（7-9像素）：
   ```python
   zscore_threshold = 3.0
   isolation_radius = 2
   min_neighbors = 2
   cleaning_method = 'interpolation'
   ```

## 输出文件

处理完成后会生成以下文件：

1. **`*_cleaned.fits`** - 清理后的FITS文件
2. **`*_noise_mask.fits`** - 噪点掩码文件
3. **`*_noise_cleaning_comparison.png`** - 可视化对比图
4. **`*_analysis.txt`** - 分析结果文件（仅完整版）

## 性能对比

| 工具版本 | 处理速度 | 功能完整性 | 稳定性 | 推荐度 |
|----------|----------|------------|--------|--------|
| 完整版 | 慢 | 高 | 中等 | ⭐⭐⭐ |
| 快速版 | 中等 | 中等 | 中等 | ⭐⭐ |
| 演示版 | 快 | 中等 | 高 | ⭐⭐⭐⭐⭐ |

## 使用建议

1. **首次使用**：推荐使用演示版工具，参数已经过优化
2. **批量处理**：可以使用完整版工具的命令行接口
3. **参数调优**：使用演示版的参数对比功能找到最佳设置
4. **特殊需求**：如需要详细的矩形度分析，使用完整版工具

## 注意事项

1. 工具主要针对**小型矩形噪点**（≤9像素）
2. 对于大面积噪声，建议使用其他专门工具
3. 处理前建议备份原始文件
4. 可视化图像使用1%-99%百分位数进行显示范围调整

## 故障排除

### 常见问题

1. **未检测到噪点**
   - 降低`zscore_threshold`值
   - 减小`min_neighbors`值
   - 检查图像是否确实包含矩形噪点

2. **检测到过多噪点**
   - 提高`zscore_threshold`值
   - 增大`isolation_radius`值
   - 使用更严格的参数设置

3. **处理速度慢**
   - 使用演示版工具
   - 关闭可视化输出
   - 减小图像尺寸

## 技术原理

工具基于以下技术：

1. **统计异常值检测**：使用MAD (Median Absolute Deviation) 计算Z-score
2. **孤立性分析**：检查异常像素的邻域特征
3. **形态学分析**：评估噪点的矩形特征（完整版）
4. **自适应清理**：根据噪点大小选择不同的清理策略

## 更新日志

- **v1.0** (2025-07-31): 初始版本，包含三种不同的实现方式
- 成功测试了`noise2.fits`文件
- 演示版工具在宽松模式下检测到11,724个噪点并成功清理
