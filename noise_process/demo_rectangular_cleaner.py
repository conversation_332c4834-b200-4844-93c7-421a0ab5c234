#!/usr/bin/env python3
"""
演示版矩形噪点清理工具
基于现有的孤立噪点清理工具，专门针对小型矩形噪点进行优化
"""

import os
import sys
from pathlib import Path
from isolated_noise_cleaner import IsolatedNoiseCleaner

def create_rectangular_noise_cleaner():
    """创建专门用于矩形噪点的清理器"""
    
    # 使用现有的孤立噪点清理器作为基础
    cleaner = IsolatedNoiseCleaner()
    
    # 针对矩形噪点优化参数
    cleaner.clean_params.update({
        # 检测参数 - 针对矩形噪点优化
        'zscore_threshold': 4.0,        # 适中的Z-score阈值
        'isolation_radius': 1,          # 小的孤立性检测半径，适合检测小型噪点
        'min_neighbors': 0,             # 降低邻居要求，检测更多孤立噪点
        'morphology_kernel_size': 1,    # 跳过形态学过滤，保留小噪点
        
        # 清理参数 - 针对小型矩形噪点
        'cleaning_method': 'median',    # 中值滤波适合处理矩形噪点
        'median_kernel_size': 3,        # 小核大小，避免过度平滑
        'gaussian_sigma': 0.8,          # 较小的高斯标准差
        'interpolation_radius': 1,      # 小的插值半径
        
        # 输出参数
        'save_visualization': True,     # 保存可视化结果
        'save_mask': True,             # 保存噪点掩码
    })
    
    return cleaner

def test_rectangular_noise_demo():
    """演示矩形噪点清理"""
    
    # 测试文件路径
    test_file = "noise2.fits"
    
    print("=" * 60)
    print("矩形噪点清理工具演示")
    print("=" * 60)
    print(f"测试文件: {test_file}")
    print("\n说明:")
    print("- 本工具基于现有的孤立噪点清理器")
    print("- 专门针对9个像素以内的小型矩形噪点进行优化")
    print("- 特点：矩形排列，边缘比较尖锐")
    
    # 检查测试文件是否存在
    if not os.path.exists(test_file):
        print(f"\n错误: 测试文件不存在: {test_file}")
        return False
    
    # 创建专门的矩形噪点清理器
    cleaner = create_rectangular_noise_cleaner()
    
    print("\n优化参数设置:")
    key_params = [
        'zscore_threshold', 'isolation_radius', 'min_neighbors', 
        'morphology_kernel_size', 'cleaning_method', 'median_kernel_size'
    ]
    for key in key_params:
        if key in cleaner.clean_params:
            print(f"  {key}: {cleaner.clean_params[key]}")
    
    print("\n" + "-" * 60)
    print("开始处理...")
    
    # 处理文件
    result = cleaner.process_fits_file(test_file)
    
    if result['success']:
        stats = result['statistics']
        print("\n" + "=" * 60)
        print("处理完成!")
        print("=" * 60)
        print(f"检测到噪点: {stats['noise_count']} 个 ({stats['noise_ratio']:.3f}%)")
        print(f"总像素数: {stats['total_pixels']:,}")
        
        print(f"\n原始图像统计:")
        print(f"  均值: {stats['original_stats']['mean']:.6f}")
        print(f"  中位数: {stats['original_stats']['median']:.6f}")
        print(f"  标准差: {stats['original_stats']['std']:.6f}")
        
        print(f"\n清理后图像统计:")
        print(f"  均值: {stats['cleaned_stats']['mean']:.6f}")
        print(f"  中位数: {stats['cleaned_stats']['median']:.6f}")
        print(f"  标准差: {stats['cleaned_stats']['std']:.6f}")
        
        print(f"\n变化统计:")
        print(f"  最大变化: {stats['changes']['max_change']:.6f}")
        print(f"  平均变化: {stats['changes']['mean_change']:.6f}")
        print(f"  噪点区域平均变化: {stats['changes']['noise_region_change']:.6f}")
        
        print(f"\n输出文件:")
        print(f"  清理后FITS: {os.path.basename(result['cleaned_fits_file'])}")
        if result['mask_fits_file']:
            print(f"  噪点掩码: {os.path.basename(result['mask_fits_file'])}")
        if result['visualization_file']:
            print(f"  可视化图: {os.path.basename(result['visualization_file'])}")
        
        # 分析结果
        print(f"\n结果分析:")
        if stats['noise_count'] > 0:
            print(f"✓ 成功检测到 {stats['noise_count']} 个噪点像素")
            print(f"✓ 噪点占总像素的 {stats['noise_ratio']:.3f}%")
            
            if stats['changes']['noise_region_change'] > 0:
                print(f"✓ 噪点区域平均修正了 {stats['changes']['noise_region_change']:.1f} 个强度单位")
            
            # 评估清理效果
            noise_change_ratio = stats['changes']['noise_region_change'] / stats['original_stats']['std']
            if noise_change_ratio > 1.0:
                print(f"✓ 清理效果显著（变化是标准差的 {noise_change_ratio:.1f} 倍）")
            elif noise_change_ratio > 0.5:
                print(f"✓ 清理效果中等（变化是标准差的 {noise_change_ratio:.1f} 倍）")
            else:
                print(f"• 清理效果较小（变化是标准差的 {noise_change_ratio:.1f} 倍）")
        else:
            print("• 未检测到符合条件的噪点")
            print("  可能原因：")
            print("  - Z-score阈值过高")
            print("  - 图像中没有明显的孤立噪点")
            print("  - 噪点不符合孤立性条件")
        
        return True
    else:
        print(f"\n处理失败: {result.get('error', '未知错误')}")
        return False

def test_parameter_comparison():
    """比较不同参数设置的效果"""
    
    test_file = "noise2.fits"
    
    if not os.path.exists(test_file):
        print(f"错误: 测试文件不存在: {test_file}")
        return
    
    print("\n" + "=" * 60)
    print("参数对比测试")
    print("=" * 60)
    
    # 测试配置
    configs = [
        {
            'name': '严格检测',
            'params': {
                'zscore_threshold': 5.0,
                'isolation_radius': 1,
                'min_neighbors': 0,
                'morphology_kernel_size': 1
            }
        },
        {
            'name': '中等检测',
            'params': {
                'zscore_threshold': 4.0,
                'isolation_radius': 1,
                'min_neighbors': 0,
                'morphology_kernel_size': 1
            }
        },
        {
            'name': '宽松检测',
            'params': {
                'zscore_threshold': 3.0,
                'isolation_radius': 2,
                'min_neighbors': 1,
                'morphology_kernel_size': 1
            }
        }
    ]
    
    results = []
    
    for config in configs:
        print(f"\n测试配置: {config['name']}")
        print("-" * 40)
        
        cleaner = create_rectangular_noise_cleaner()
        cleaner.clean_params.update(config['params'])
        cleaner.clean_params['save_visualization'] = False  # 节省时间
        cleaner.clean_params['save_mask'] = False
        
        # 创建专门的输出目录
        program_dir = Path(__file__).parent
        output_dir = program_dir / f"demo_rectangular_{config['name'].replace(' ', '_')}_noise2"
        
        result = cleaner.process_fits_file(test_file, str(output_dir))
        
        if result['success']:
            stats = result['statistics']
            print(f"  检测噪点: {stats['noise_count']} 个 ({stats['noise_ratio']:.3f}%)")
            print(f"  平均变化: {stats['changes']['mean_change']:.6f}")
            print(f"  噪点区域变化: {stats['changes']['noise_region_change']:.6f}")
            
            results.append({
                'name': config['name'],
                'noise_count': stats['noise_count'],
                'noise_ratio': stats['noise_ratio'],
                'mean_change': stats['changes']['mean_change'],
                'noise_change': stats['changes']['noise_region_change']
            })
        else:
            print(f"  处理失败: {result.get('error', '未知错误')}")
    
    # 总结对比
    if results:
        print(f"\n" + "=" * 60)
        print("对比总结")
        print("=" * 60)
        print(f"{'配置':<12} {'噪点数':<8} {'噪点比例':<10} {'平均变化':<12} {'噪点变化':<12}")
        print("-" * 60)
        for r in results:
            print(f"{r['name']:<12} {r['noise_count']:<8} {r['noise_ratio']:<10.3f} "
                  f"{r['mean_change']:<12.6f} {r['noise_change']:<12.6f}")

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == '--compare':
        test_parameter_comparison()
    else:
        success = test_rectangular_noise_demo()
        if not success:
            sys.exit(1)
