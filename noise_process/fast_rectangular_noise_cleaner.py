#!/usr/bin/env python3
"""
快速矩形噪点清理工具
使用滑动窗口方法直接检测9个像素以内的矩形噪点
"""

import os
import sys
import numpy as np
import logging
from datetime import datetime
from pathlib import Path
import matplotlib.pyplot as plt
from scipy import ndimage
from astropy.io import fits
from astropy.stats import sigma_clipped_stats, mad_std
import warnings

# 忽略警告
warnings.filterwarnings('ignore', category=RuntimeWarning)
warnings.filterwarnings('ignore', category=UserWarning)


class FastRectangularNoiseCleaner:
    """快速矩形噪点清理器"""
    
    def __init__(self, log_level=logging.INFO):
        """初始化"""
        self.setup_logging(log_level)
        
        # 默认参数
        self.clean_params = {
            'zscore_threshold': 6.0,        # Z-score阈值
            'max_window_size': 3,           # 最大窗口大小（3x3=9像素）
            'min_window_size': 1,           # 最小窗口大小
            'edge_threshold': 0.5,          # 边缘检测阈值
            'cleaning_method': 'median',    # 清理方法
            'median_kernel_size': 3,        # 中值滤波核大小
            'save_visualization': True,     # 保存可视化结果
            'save_mask': True,             # 保存噪点掩码
        }
        
        self.logger.info("快速矩形噪点清理器初始化完成")
    
    def setup_logging(self, log_level):
        """设置日志"""
        self.logger = logging.getLogger('FastRectangularNoiseCleaner')
        self.logger.setLevel(log_level)
        
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def load_fits_data(self, fits_path):
        """加载FITS文件数据"""
        try:
            with fits.open(fits_path) as hdul:
                data = hdul[0].data.astype(np.float64)
                header = hdul[0].header
                
                if len(data.shape) == 3:
                    data = data[0]
                
                self.logger.info(f"成功加载FITS文件: {os.path.basename(fits_path)}")
                self.logger.info(f"数据形状: {data.shape}, 数据范围: [{np.min(data):.6f}, {np.max(data):.6f}]")
                
                return data, header
                
        except Exception as e:
            self.logger.error(f"加载FITS文件失败 {fits_path}: {str(e)}")
            return None, None
    
    def detect_rectangular_noise_fast(self, image_data):
        """快速检测矩形噪点"""
        try:
            self.logger.info("开始快速检测矩形噪点...")
            
            # 1. 计算背景统计
            mean, median, std = sigma_clipped_stats(image_data, sigma=3.0, maxiters=5)
            mad = mad_std(image_data)
            
            if mad > 0:
                threshold = median + self.clean_params['zscore_threshold'] * mad
            else:
                threshold = mean + self.clean_params['zscore_threshold'] * std
            
            self.logger.info(f"噪点检测阈值: {threshold:.6f}")
            
            # 2. 使用滑动窗口检测矩形噪点
            noise_mask = np.zeros_like(image_data, dtype=bool)
            detected_regions = []
            
            max_size = self.clean_params['max_window_size']
            
            # 遍历不同大小的矩形窗口
            for window_h in range(1, max_size + 1):
                for window_w in range(1, max_size + 1):
                    if window_h * window_w > 9:  # 限制最大9个像素
                        continue
                    
                    regions = self._detect_rectangular_regions(
                        image_data, threshold, window_h, window_w
                    )
                    
                    for region in regions:
                        y, x, h, w = region
                        region_mask = np.zeros_like(image_data, dtype=bool)
                        region_mask[y:y+h, x:x+w] = True
                        
                        # 检查是否与已检测区域重叠
                        if not np.any(noise_mask & region_mask):
                            noise_mask |= region_mask
                            detected_regions.append({
                                'bbox': (y, y+h-1, x, x+w-1),
                                'size': h * w,
                                'shape': (h, w),
                                'rectangularity': 1.0,  # 完美矩形
                                'mean_intensity': np.mean(image_data[y:y+h, x:x+w])
                            })
            
            noise_count = np.sum(noise_mask)
            total_pixels = image_data.size
            noise_ratio = noise_count / total_pixels * 100
            
            self.logger.info(f"检测到 {noise_count} 个矩形噪点像素 ({noise_ratio:.3f}%)")
            self.logger.info(f"检测到 {len(detected_regions)} 个矩形噪点区域")
            
            return noise_mask, detected_regions
            
        except Exception as e:
            self.logger.error(f"矩形噪点检测失败: {str(e)}")
            return np.zeros_like(image_data, dtype=bool), []
    
    def _detect_rectangular_regions(self, image_data, threshold, window_h, window_w):
        """检测指定大小的矩形区域"""
        regions = []
        h, w = image_data.shape
        
        # 滑动窗口
        for y in range(0, h - window_h + 1, window_h):  # 不重叠的窗口
            for x in range(0, w - window_w + 1, window_w):
                window = image_data[y:y+window_h, x:x+window_w]
                
                # 检查窗口内所有像素是否都超过阈值
                if np.all(window > threshold):
                    # 检查边缘锐度（与周围像素的对比）
                    if self._check_edge_sharpness(image_data, y, x, window_h, window_w, threshold):
                        regions.append((y, x, window_h, window_w))
        
        return regions
    
    def _check_edge_sharpness(self, image_data, y, x, h, w, threshold):
        """检查边缘锐度"""
        try:
            # 扩展边界以包含周围像素
            pad = 1
            y_start = max(0, y - pad)
            y_end = min(image_data.shape[0], y + h + pad)
            x_start = max(0, x - pad)
            x_end = min(image_data.shape[1], x + w + pad)
            
            # 提取扩展区域
            extended_region = image_data[y_start:y_end, x_start:x_end]
            
            # 创建内部和边界掩码
            inner_y_start = y - y_start
            inner_y_end = inner_y_start + h
            inner_x_start = x - x_start
            inner_x_end = inner_x_start + w
            
            inner_mask = np.zeros_like(extended_region, dtype=bool)
            inner_mask[inner_y_start:inner_y_end, inner_x_start:inner_x_end] = True
            
            # 计算内部和外部的平均强度
            inner_mean = np.mean(extended_region[inner_mask])
            outer_mean = np.mean(extended_region[~inner_mask])
            
            # 检查对比度
            contrast = abs(inner_mean - outer_mean) / (outer_mean + 1e-6)
            
            return contrast > self.clean_params['edge_threshold']
            
        except Exception:
            return True  # 如果计算失败，默认通过
    
    def clean_noise(self, image_data, noise_mask):
        """清理噪点"""
        try:
            self.logger.info("开始清理矩形噪点...")
            
            cleaned_data = image_data.copy()
            method = self.clean_params['cleaning_method']
            
            if method == 'median':
                kernel_size = self.clean_params['median_kernel_size']
                filtered_image = ndimage.median_filter(image_data, size=kernel_size)
                cleaned_data[noise_mask] = filtered_image[noise_mask]
            
            self.logger.info("矩形噪点清理完成")
            return cleaned_data
            
        except Exception as e:
            self.logger.error(f"矩形噪点清理失败: {str(e)}")
            return image_data
    
    def save_fits_file(self, data, header, output_path):
        """保存FITS文件"""
        try:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            hdu = fits.PrimaryHDU(data=data, header=header)
            hdu.writeto(output_path, overwrite=True)
            self.logger.info(f"FITS文件已保存: {output_path}")
        except Exception as e:
            self.logger.error(f"保存FITS文件失败: {str(e)}")
    
    def create_visualization(self, original_data, cleaned_data, noise_mask, detected_regions, output_path):
        """创建可视化对比图"""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            
            vmin = np.percentile(original_data, 1)
            vmax = np.percentile(original_data, 99)
            
            # 原始图像
            im1 = axes[0, 0].imshow(original_data, cmap='gray', vmin=vmin, vmax=vmax)
            axes[0, 0].set_title('原始图像')
            axes[0, 0].axis('off')
            plt.colorbar(im1, ax=axes[0, 0])
            
            # 噪点掩码
            axes[0, 1].imshow(original_data, cmap='gray', vmin=vmin, vmax=vmax, alpha=0.7)
            axes[0, 1].imshow(noise_mask, cmap='Reds', alpha=0.8)
            axes[0, 1].set_title(f'检测到的矩形噪点 ({np.sum(noise_mask)} 个像素)')
            axes[0, 1].axis('off')
            
            # 清理后图像
            im3 = axes[1, 0].imshow(cleaned_data, cmap='gray', vmin=vmin, vmax=vmax)
            axes[1, 0].set_title('清理后图像')
            axes[1, 0].axis('off')
            plt.colorbar(im3, ax=axes[1, 0])
            
            # 差异图像
            difference = original_data - cleaned_data
            im4 = axes[1, 1].imshow(difference, cmap='RdBu_r',
                                  vmin=-np.std(difference)*3, vmax=np.std(difference)*3)
            axes[1, 1].set_title('差异图像')
            axes[1, 1].axis('off')
            plt.colorbar(im4, ax=axes[1, 1])
            
            plt.tight_layout()
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"可视化结果已保存: {output_path}")
            
        except Exception as e:
            self.logger.error(f"创建可视化失败: {str(e)}")
    
    def process_fits_file(self, input_path, output_dir=None):
        """处理单个FITS文件"""
        try:
            self.logger.info(f"开始处理FITS文件: {input_path}")
            
            # 加载数据
            original_data, header = self.load_fits_data(input_path)
            if original_data is None:
                return {'success': False, 'error': '无法加载FITS文件'}
            
            # 检测矩形噪点
            noise_mask, detected_regions = self.detect_rectangular_noise_fast(original_data)
            
            # 清理噪点
            cleaned_data = self.clean_noise(original_data, noise_mask)
            
            # 准备输出路径
            input_name = Path(input_path).stem
            if output_dir is None:
                program_dir = Path(__file__).parent
                output_dir = program_dir / f"fast_rectangular_cleaned_{input_name}"
            else:
                output_dir = Path(output_dir)
            
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存文件
            cleaned_fits_path = output_dir / f"{input_name}_fast_rectangular_cleaned.fits"
            self.save_fits_file(cleaned_data, header, str(cleaned_fits_path))
            
            mask_fits_path = None
            if self.clean_params['save_mask']:
                mask_fits_path = output_dir / f"{input_name}_fast_rectangular_mask.fits"
                mask_header = header.copy()
                mask_header['COMMENT'] = 'Fast rectangular noise mask: 1=noise, 0=clean'
                self.save_fits_file(noise_mask.astype(np.uint8), mask_header, str(mask_fits_path))
            
            visualization_path = None
            if self.clean_params['save_visualization']:
                visualization_path = output_dir / f"{input_name}_fast_rectangular_analysis.png"
                self.create_visualization(original_data, cleaned_data, noise_mask, 
                                        detected_regions, str(visualization_path))
            
            # 计算统计信息
            noise_count = np.sum(noise_mask)
            total_pixels = original_data.size
            noise_ratio = noise_count / total_pixels * 100
            
            difference = original_data - cleaned_data
            max_change = np.max(np.abs(difference))
            mean_change = np.mean(np.abs(difference))
            
            stats = {
                'noise_count': int(noise_count),
                'total_pixels': int(total_pixels),
                'noise_ratio': float(noise_ratio),
                'num_noise_regions': len(detected_regions),
                'max_change': float(max_change),
                'mean_change': float(mean_change)
            }
            
            result = {
                'success': True,
                'input_file': input_path,
                'cleaned_fits_file': str(cleaned_fits_path),
                'mask_fits_file': str(mask_fits_path) if mask_fits_path else None,
                'visualization_file': str(visualization_path) if visualization_path else None,
                'statistics': stats,
                'detected_regions': detected_regions
            }
            
            self.logger.info(f"处理完成: {input_path}")
            return result
            
        except Exception as e:
            self.logger.error(f"处理FITS文件失败: {str(e)}")
            return {'success': False, 'error': str(e)}
