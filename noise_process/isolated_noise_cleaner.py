#!/usr/bin/env python3
"""
FITS文件孤立噪点清理工具
用于检测和清理FITS图像中的孤立噪点
"""

import os
import sys
import numpy as np
import logging
from datetime import datetime
from pathlib import Path
import matplotlib.pyplot as plt
from matplotlib.colors import LogNorm
import cv2
from scipy import ndimage
from scipy.stats import zscore
from skimage import morphology, filters, measure
from astropy.io import fits
from astropy.stats import sigma_clipped_stats, mad_std
import warnings

# 忽略警告
warnings.filterwarnings('ignore', category=RuntimeWarning)
warnings.filterwarnings('ignore', category=UserWarning)


class IsolatedNoiseCleaner:
    """FITS文件孤立噪点清理器"""
    
    def __init__(self, log_level=logging.INFO):
        """
        初始化噪点清理器
        
        Args:
            log_level: 日志级别
        """
        self.setup_logging(log_level)
        
        # 默认参数
        self.clean_params = {
            # 噪点检测参数
            'zscore_threshold': 3.0,        # Z-score阈值
            'isolation_radius': 1,          # 孤立性检测半径
            'min_neighbors': 1,             # 最小邻居数量
            'morphology_kernel_size': 1,    # 形态学核大小
            
            # 清理参数
            'cleaning_method': 'median',    # 清理方法: median, gaussian, mean
            'median_kernel_size': 5,        # 中值滤波核大小
            'gaussian_sigma': 1.0,          # 高斯滤波标准差
            'interpolation_radius': 2,      # 插值半径
            
            # 输出参数
            'save_visualization': True,     # 保存可视化结果
            'save_mask': True,             # 保存噪点掩码
        }
        
        self.logger.info("孤立噪点清理器初始化完成")
    
    def setup_logging(self, log_level):
        """设置日志"""
        self.logger = logging.getLogger('IsolatedNoiseCleaner')
        self.logger.setLevel(log_level)
        
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def load_fits_data(self, fits_path):
        """
        加载FITS文件数据
        
        Args:
            fits_path (str): FITS文件路径
            
        Returns:
            tuple: (图像数据, FITS头信息)，如果失败返回(None, None)
        """
        try:
            with fits.open(fits_path) as hdul:
                data = hdul[0].data.astype(np.float64)
                header = hdul[0].header
                
                # 处理可能的3D数据（取第一个通道）
                if len(data.shape) == 3:
                    data = data[0]
                
                self.logger.info(f"成功加载FITS文件: {os.path.basename(fits_path)}")
                self.logger.info(f"数据形状: {data.shape}, 数据范围: [{np.min(data):.6f}, {np.max(data):.6f}]")
                
                return data, header
                
        except Exception as e:
            self.logger.error(f"加载FITS文件失败 {fits_path}: {str(e)}")
            return None, None
    
    def detect_isolated_noise(self, image_data):
        """
        检测孤立噪点
        
        Args:
            image_data (np.ndarray): 输入图像数据
            
        Returns:
            np.ndarray: 噪点掩码（True表示噪点）
        """
        try:
            self.logger.info("开始检测孤立噪点...")
            
            # 1. 统计异常值检测
            noise_mask = self._detect_statistical_outliers(image_data)
            
            # 2. 孤立性检测
            isolated_mask = self._detect_isolation(image_data, noise_mask)
            
            # 3. 形态学过滤
            final_mask = self._morphological_filtering(isolated_mask)
            
            noise_count = np.sum(final_mask)
            total_pixels = image_data.size
            noise_ratio = noise_count / total_pixels * 100
            
            self.logger.info(f"检测到 {noise_count} 个孤立噪点 ({noise_ratio:.3f}%)")
            
            return final_mask
            
        except Exception as e:
            self.logger.error(f"噪点检测失败: {str(e)}")
            return np.zeros_like(image_data, dtype=bool)
    
    def _detect_statistical_outliers(self, image_data):
        """使用统计方法检测异常值"""
        # 计算背景统计
        mean, median, std = sigma_clipped_stats(image_data, sigma=3.0, maxiters=5)
        mad = mad_std(image_data)
        
        # 使用MAD-based Z-score检测异常值
        if mad > 0:
            z_scores = np.abs((image_data - median) / mad)
            outlier_mask = z_scores > self.clean_params['zscore_threshold']
        else:
            # 备用方法：使用标准差
            if std > 0:
                z_scores = np.abs((image_data - mean) / std)
                outlier_mask = z_scores > self.clean_params['zscore_threshold']
            else:
                outlier_mask = np.zeros_like(image_data, dtype=bool)
        
        self.logger.info(f"统计异常值检测: {np.sum(outlier_mask)} 个像素")
        return outlier_mask
    
    def _detect_isolation(self, image_data, candidate_mask):
        """检测孤立性"""
        isolated_mask = np.zeros_like(candidate_mask, dtype=bool)
        radius = self.clean_params['isolation_radius']
        min_neighbors = self.clean_params['min_neighbors']
        
        # 为每个候选噪点检查邻域
        candidate_coords = np.where(candidate_mask)
        
        for i, (y, x) in enumerate(zip(candidate_coords[0], candidate_coords[1])):
            # 定义邻域
            y_min = max(0, y - radius)
            y_max = min(image_data.shape[0], y + radius + 1)
            x_min = max(0, x - radius)
            x_max = min(image_data.shape[1], x + radius + 1)
            
            # 计算邻域内的邻居数量（排除中心像素）
            neighborhood = candidate_mask[y_min:y_max, x_min:x_max].copy()
            center_y = y - y_min
            center_x = x - x_min
            if center_y < neighborhood.shape[0] and center_x < neighborhood.shape[1]:
                neighborhood[center_y, center_x] = False
            
            neighbor_count = np.sum(neighborhood)
            
            # 如果邻居数量少于阈值，则认为是孤立噪点
            if neighbor_count < min_neighbors:
                isolated_mask[y, x] = True
        
        self.logger.info(f"孤立性检测: {np.sum(isolated_mask)} 个孤立噪点")
        return isolated_mask
    
    def _morphological_filtering(self, mask):
        """形态学过滤"""
        kernel_size = self.clean_params['morphology_kernel_size']

        # 如果核大小为1或更小，跳过形态学过滤
        if kernel_size <= 1:
            self.logger.info(f"跳过形态学过滤: {np.sum(mask)} 个噪点保留")
            return mask

        kernel = morphology.disk(kernel_size)

        # 开运算去除小的噪声
        filtered_mask = morphology.opening(mask, kernel)

        self.logger.info(f"形态学过滤: {np.sum(filtered_mask)} 个噪点保留")
        return filtered_mask
    
    def clean_noise(self, image_data, noise_mask):
        """
        清理噪点
        
        Args:
            image_data (np.ndarray): 原始图像数据
            noise_mask (np.ndarray): 噪点掩码
            
        Returns:
            np.ndarray: 清理后的图像数据
        """
        try:
            self.logger.info("开始清理噪点...")
            
            cleaned_data = image_data.copy()
            method = self.clean_params['cleaning_method']
            
            if method == 'median':
                cleaned_data = self._median_cleaning(cleaned_data, noise_mask)
            elif method == 'gaussian':
                cleaned_data = self._gaussian_cleaning(cleaned_data, noise_mask)
            elif method == 'mean':
                cleaned_data = self._mean_cleaning(cleaned_data, noise_mask)
            else:
                self.logger.warning(f"未知的清理方法: {method}，使用中值滤波")
                cleaned_data = self._median_cleaning(cleaned_data, noise_mask)
            
            self.logger.info("噪点清理完成")
            return cleaned_data
            
        except Exception as e:
            self.logger.error(f"噪点清理失败: {str(e)}")
            return image_data
    
    def _median_cleaning(self, image_data, noise_mask):
        """中值滤波清理"""
        kernel_size = self.clean_params['median_kernel_size']
        
        # 对整个图像应用中值滤波
        filtered_image = ndimage.median_filter(image_data, size=kernel_size)
        
        # 只在噪点位置使用滤波结果
        cleaned_data = image_data.copy()
        cleaned_data[noise_mask] = filtered_image[noise_mask]
        
        return cleaned_data
    
    def _gaussian_cleaning(self, image_data, noise_mask):
        """高斯滤波清理"""
        sigma = self.clean_params['gaussian_sigma']
        
        # 对整个图像应用高斯滤波
        filtered_image = ndimage.gaussian_filter(image_data, sigma=sigma)
        
        # 只在噪点位置使用滤波结果
        cleaned_data = image_data.copy()
        cleaned_data[noise_mask] = filtered_image[noise_mask]
        
        return cleaned_data
    
    def _mean_cleaning(self, image_data, noise_mask):
        """邻域平均清理"""
        radius = self.clean_params['interpolation_radius']
        cleaned_data = image_data.copy()
        
        # 为每个噪点计算邻域平均值
        noise_coords = np.where(noise_mask)
        
        for y, x in zip(noise_coords[0], noise_coords[1]):
            # 定义邻域（排除噪点本身）
            y_min = max(0, y - radius)
            y_max = min(image_data.shape[0], y + radius + 1)
            x_min = max(0, x - radius)
            x_max = min(image_data.shape[1], x + radius + 1)
            
            neighborhood = image_data[y_min:y_max, x_min:x_max]
            neighborhood_mask = noise_mask[y_min:y_max, x_min:x_max]
            
            # 只使用非噪点像素计算平均值
            valid_pixels = neighborhood[~neighborhood_mask]
            if len(valid_pixels) > 0:
                cleaned_data[y, x] = np.mean(valid_pixels)
        
        return cleaned_data

    def save_fits_file(self, data, header, output_path):
        """
        保存FITS文件

        Args:
            data (np.ndarray): 图像数据
            header: FITS头信息
            output_path (str): 输出路径
        """
        try:
            # 创建输出目录
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 保存FITS文件
            hdu = fits.PrimaryHDU(data=data, header=header)
            hdu.writeto(output_path, overwrite=True)

            self.logger.info(f"FITS文件已保存: {output_path}")

        except Exception as e:
            self.logger.error(f"保存FITS文件失败: {str(e)}")

    def create_visualization(self, original_data, cleaned_data, noise_mask, output_path):
        """
        创建可视化对比图

        Args:
            original_data (np.ndarray): 原始图像数据
            cleaned_data (np.ndarray): 清理后图像数据
            noise_mask (np.ndarray): 噪点掩码
            output_path (str): 输出路径
        """
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))

            # 计算显示范围
            vmin = np.percentile(original_data, 1)
            vmax = np.percentile(original_data, 99)

            # 原始图像
            im1 = axes[0, 0].imshow(original_data, cmap='gray', vmin=vmin, vmax=vmax)
            axes[0, 0].set_title('原始图像')
            axes[0, 0].axis('off')
            plt.colorbar(im1, ax=axes[0, 0])

            # 噪点掩码
            im2 = axes[0, 1].imshow(noise_mask, cmap='Reds', alpha=0.7)
            axes[0, 1].imshow(original_data, cmap='gray', vmin=vmin, vmax=vmax, alpha=0.3)
            axes[0, 1].set_title(f'检测到的噪点 ({np.sum(noise_mask)} 个)')
            axes[0, 1].axis('off')

            # 清理后图像
            im3 = axes[1, 0].imshow(cleaned_data, cmap='gray', vmin=vmin, vmax=vmax)
            axes[1, 0].set_title('清理后图像')
            axes[1, 0].axis('off')
            plt.colorbar(im3, ax=axes[1, 0])

            # 差异图像
            difference = original_data - cleaned_data
            im4 = axes[1, 1].imshow(difference, cmap='RdBu_r',
                                  vmin=-np.std(difference)*3, vmax=np.std(difference)*3)
            axes[1, 1].set_title('差异图像 (原始 - 清理后)')
            axes[1, 1].axis('off')
            plt.colorbar(im4, ax=axes[1, 1])

            plt.tight_layout()
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            plt.close()

            self.logger.info(f"可视化结果已保存: {output_path}")

        except Exception as e:
            self.logger.error(f"创建可视化失败: {str(e)}")

    def calculate_statistics(self, original_data, cleaned_data, noise_mask):
        """
        计算处理统计信息

        Args:
            original_data (np.ndarray): 原始图像数据
            cleaned_data (np.ndarray): 清理后图像数据
            noise_mask (np.ndarray): 噪点掩码

        Returns:
            dict: 统计信息
        """
        try:
            # 基本统计
            noise_count = np.sum(noise_mask)
            total_pixels = original_data.size
            noise_ratio = noise_count / total_pixels * 100

            # 图像质量指标
            original_mean, original_median, original_std = sigma_clipped_stats(original_data, sigma=3.0)
            cleaned_mean, cleaned_median, cleaned_std = sigma_clipped_stats(cleaned_data, sigma=3.0)

            # 差异统计
            difference = original_data - cleaned_data
            max_change = np.max(np.abs(difference))
            mean_change = np.mean(np.abs(difference))

            # 噪点区域的变化
            if noise_count > 0:
                noise_original_values = original_data[noise_mask]
                noise_cleaned_values = cleaned_data[noise_mask]
                noise_change = np.mean(np.abs(noise_original_values - noise_cleaned_values))
            else:
                noise_change = 0.0

            stats = {
                'noise_count': int(noise_count),
                'total_pixels': int(total_pixels),
                'noise_ratio': float(noise_ratio),
                'original_stats': {
                    'mean': float(original_mean),
                    'median': float(original_median),
                    'std': float(original_std)
                },
                'cleaned_stats': {
                    'mean': float(cleaned_mean),
                    'median': float(cleaned_median),
                    'std': float(cleaned_std)
                },
                'changes': {
                    'max_change': float(max_change),
                    'mean_change': float(mean_change),
                    'noise_region_change': float(noise_change)
                }
            }

            return stats

        except Exception as e:
            self.logger.error(f"计算统计信息失败: {str(e)}")
            return {}

    def process_fits_file(self, input_path, output_dir=None):
        """
        处理单个FITS文件

        Args:
            input_path (str): 输入FITS文件路径
            output_dir (str): 输出目录，如果为None则在程序所在目录下创建与文件名相关的目录

        Returns:
            dict: 处理结果
        """
        try:
            self.logger.info(f"开始处理FITS文件: {input_path}")

            # 加载数据
            original_data, header = self.load_fits_data(input_path)
            if original_data is None:
                return {'success': False, 'error': '无法加载FITS文件'}

            # 检测噪点
            noise_mask = self.detect_isolated_noise(original_data)

            # 清理噪点
            cleaned_data = self.clean_noise(original_data, noise_mask)

            # 计算统计信息
            stats = self.calculate_statistics(original_data, cleaned_data, noise_mask)

            # 准备输出路径
            input_name = Path(input_path).stem
            if output_dir is None:
                # 默认在程序文件所在目录下创建与文件名相关的目录
                program_dir = Path(__file__).parent
                output_dir = program_dir / f"noise_cleaned_{input_name}"
                self.logger.info(f"使用默认输出目录: {output_dir}")
            else:
                output_dir = Path(output_dir)

            output_dir.mkdir(parents=True, exist_ok=True)

            # 保存清理后的FITS文件
            cleaned_fits_path = output_dir / f"{input_name}_cleaned.fits"
            self.save_fits_file(cleaned_data, header, str(cleaned_fits_path))

            # 保存噪点掩码（如果启用）
            mask_fits_path = None
            if self.clean_params['save_mask']:
                mask_fits_path = output_dir / f"{input_name}_noise_mask.fits"
                mask_header = header.copy()
                mask_header['COMMENT'] = 'Noise mask: 1=noise, 0=clean'
                self.save_fits_file(noise_mask.astype(np.uint8), mask_header, str(mask_fits_path))

            # 创建可视化（如果启用）
            visualization_path = None
            if self.clean_params['save_visualization']:
                visualization_path = output_dir / f"{input_name}_noise_cleaning_comparison.png"
                self.create_visualization(original_data, cleaned_data, noise_mask, str(visualization_path))

            result = {
                'success': True,
                'input_file': input_path,
                'cleaned_fits_file': str(cleaned_fits_path),
                'mask_fits_file': str(mask_fits_path) if mask_fits_path else None,
                'visualization_file': str(visualization_path) if visualization_path else None,
                'statistics': stats,
                'parameters': self.clean_params.copy()
            }

            self.logger.info(f"处理完成: {input_path}")
            return result

        except Exception as e:
            self.logger.error(f"处理FITS文件失败: {str(e)}")
            return {'success': False, 'error': str(e)}


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(
        description='FITS文件孤立噪点清理工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python isolated_noise_cleaner.py --input image.fits
  python isolated_noise_cleaner.py --input image.fits --output ./cleaned/
  python isolated_noise_cleaner.py --input image.fits --method gaussian --threshold 3.5

清理方法说明:
  median   - 中值滤波清理（默认）
  gaussian - 高斯滤波清理
  mean     - 邻域平均清理
        """
    )

    parser.add_argument('--input', '-i', required=True,
                       help='输入FITS文件路径')
    parser.add_argument('--output', '-o',
                       help='输出目录（默认在程序目录下创建noise_cleaned_<文件名>目录）')
    parser.add_argument('--method', '-m', choices=['median', 'gaussian', 'mean'],
                       default='median',
                       help='清理方法（默认: median）')
    parser.add_argument('--threshold', '-t', type=float, default=5.0,
                       help='Z-score阈值（默认: 5.0）')
    parser.add_argument('--isolation-radius', type=int, default=2,
                       help='孤立性检测半径（默认: 2）')
    parser.add_argument('--min-neighbors', type=int, default=1,
                       help='最小邻居数量（默认: 1）')
    parser.add_argument('--no-visualization', action='store_true',
                       help='不保存可视化结果')
    parser.add_argument('--no-mask', action='store_true',
                       help='不保存噪点掩码')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='详细输出')

    args = parser.parse_args()

    # 设置日志级别
    log_level = logging.DEBUG if args.verbose else logging.INFO

    print("=" * 60)
    print("FITS文件孤立噪点清理工具")
    print("=" * 60)
    print(f"输入文件: {args.input}")
    print(f"清理方法: {args.method}")
    print(f"Z-score阈值: {args.threshold}")
    print(f"孤立性检测半径: {args.isolation_radius}")
    print(f"最小邻居数量: {args.min_neighbors}")
    print("-" * 60)

    # 检查输入文件
    if not os.path.exists(args.input):
        print(f"错误: 输入文件不存在: {args.input}")
        sys.exit(1)

    # 创建清理器
    cleaner = IsolatedNoiseCleaner(log_level=log_level)

    # 设置参数
    cleaner.clean_params['cleaning_method'] = args.method
    cleaner.clean_params['zscore_threshold'] = args.threshold
    cleaner.clean_params['isolation_radius'] = args.isolation_radius
    cleaner.clean_params['min_neighbors'] = args.min_neighbors
    cleaner.clean_params['morphology_kernel_size'] = 1  # 默认跳过形态学过滤
    cleaner.clean_params['median_kernel_size'] = 3      # 使用较小的中值滤波核
    cleaner.clean_params['save_visualization'] = not args.no_visualization
    cleaner.clean_params['save_mask'] = not args.no_mask

    # 处理文件
    result = cleaner.process_fits_file(args.input, args.output)

    if result['success']:
        stats = result['statistics']
        print("\n" + "=" * 60)
        print("处理完成!")
        print("=" * 60)
        print(f"检测到噪点: {stats['noise_count']} 个 ({stats['noise_ratio']:.3f}%)")
        print(f"最大变化: {stats['changes']['max_change']:.6f}")
        print(f"平均变化: {stats['changes']['mean_change']:.6f}")
        print(f"噪点区域平均变化: {stats['changes']['noise_region_change']:.6f}")
        print(f"\n输出文件:")
        print(f"  清理后FITS: {os.path.basename(result['cleaned_fits_file'])}")
        if result['mask_fits_file']:
            print(f"  噪点掩码: {os.path.basename(result['mask_fits_file'])}")
        if result['visualization_file']:
            print(f"  可视化图: {os.path.basename(result['visualization_file'])}")
    else:
        print(f"\n处理失败: {result.get('error', '未知错误')}")
        sys.exit(1)


if __name__ == '__main__':
    main()
