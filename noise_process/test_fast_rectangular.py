#!/usr/bin/env python3
"""
测试快速矩形噪点清理工具
"""

import os
import sys
from pathlib import Path
from fast_rectangular_noise_cleaner import FastRectangularNoiseCleaner

def test_fast_rectangular():
    """测试快速矩形噪点清理工具"""
    
    # 测试文件路径
    test_file = "noise2.fits"
    
    print("=" * 60)
    print("快速矩形噪点清理工具测试")
    print("=" * 60)
    print(f"测试文件: {test_file}")
    
    # 检查测试文件是否存在
    if not os.path.exists(test_file):
        print(f"错误: 测试文件不存在: {test_file}")
        return False
    
    # 创建清理器
    cleaner = FastRectangularNoiseCleaner()
    
    # 设置参数
    cleaner.clean_params.update({
        'zscore_threshold': 6.0,            # Z-score阈值
        'max_window_size': 3,               # 最大窗口大小（3x3=9像素）
        'min_window_size': 1,               # 最小窗口大小
        'edge_threshold': 0.5,              # 边缘检测阈值
        'cleaning_method': 'median',        # 清理方法
        'median_kernel_size': 3,            # 中值滤波核大小
        'save_visualization': True,         # 保存可视化结果
        'save_mask': True,                 # 保存噪点掩码
    })
    
    print("\n参数设置:")
    for key, value in cleaner.clean_params.items():
        print(f"  {key}: {value}")
    
    print("\n" + "-" * 60)
    print("开始处理...")
    
    # 处理文件
    result = cleaner.process_fits_file(test_file)
    
    if result['success']:
        stats = result['statistics']
        print("\n" + "=" * 60)
        print("处理完成!")
        print("=" * 60)
        print(f"检测到矩形噪点: {stats['noise_count']} 个像素 ({stats['noise_ratio']:.3f}%)")
        print(f"矩形噪点区域: {stats['num_noise_regions']} 个")
        print(f"总像素数: {stats['total_pixels']:,}")
        
        print(f"\n图像变化:")
        print(f"  最大变化: {stats['max_change']:.6f}")
        print(f"  平均变化: {stats['mean_change']:.6f}")
        
        print(f"\n输出文件:")
        print(f"  清理后FITS: {result['cleaned_fits_file']}")
        if result['mask_fits_file']:
            print(f"  噪点掩码: {result['mask_fits_file']}")
        if result['visualization_file']:
            print(f"  可视化图: {result['visualization_file']}")
        
        # 显示检测到的噪点详情
        if result['detected_regions']:
            print(f"\n前10个检测到的矩形噪点详情:")
            print("-" * 80)
            print(f"{'ID':<4} {'大小':<6} {'形状':<8} {'矩形度':<8} {'平均强度':<12} {'边界框':<20}")
            print("-" * 80)
            for i, region in enumerate(result['detected_regions'][:10]):
                bbox = region['bbox']
                bbox_str = f"({bbox[0]}-{bbox[1]}, {bbox[2]}-{bbox[3]})"
                shape_str = f"{region['shape'][0]}x{region['shape'][1]}"
                print(f"{i+1:<4} {region['size']:<6} {shape_str:<8} {region['rectangularity']:<8.3f} "
                     f"{region['mean_intensity']:<12.1f} {bbox_str:<20}")
            
            if len(result['detected_regions']) > 10:
                print(f"... 还有 {len(result['detected_regions']) - 10} 个区域")
        
        return True
    else:
        print(f"\n处理失败: {result.get('error', '未知错误')}")
        return False

def test_different_thresholds():
    """测试不同的阈值设置"""
    
    test_file = "noise2.fits"
    
    if not os.path.exists(test_file):
        print(f"错误: 测试文件不存在: {test_file}")
        return
    
    thresholds = [5.0, 6.0, 7.0, 8.0]
    
    print("=" * 60)
    print("测试不同Z-score阈值")
    print("=" * 60)
    
    for threshold in thresholds:
        print(f"\n测试阈值: {threshold}")
        print("-" * 40)
        
        cleaner = FastRectangularNoiseCleaner()
        cleaner.clean_params['zscore_threshold'] = threshold
        cleaner.clean_params['save_visualization'] = False  # 节省时间
        cleaner.clean_params['save_mask'] = False
        
        result = cleaner.process_fits_file(test_file)
        
        if result['success']:
            stats = result['statistics']
            print(f"  检测到矩形噪点区域: {stats['num_noise_regions']} 个")
            print(f"  噪点像素: {stats['noise_count']} 个 ({stats['noise_ratio']:.3f}%)")
            print(f"  平均变化: {stats['mean_change']:.6f}")
        else:
            print(f"  处理失败: {result.get('error', '未知错误')}")

def test_different_window_sizes():
    """测试不同的窗口大小"""
    
    test_file = "noise2.fits"
    
    if not os.path.exists(test_file):
        print(f"错误: 测试文件不存在: {test_file}")
        return
    
    window_sizes = [1, 2, 3]
    
    print("=" * 60)
    print("测试不同最大窗口大小")
    print("=" * 60)
    
    for window_size in window_sizes:
        print(f"\n测试最大窗口大小: {window_size}x{window_size} (最大{window_size*window_size}像素)")
        print("-" * 50)
        
        cleaner = FastRectangularNoiseCleaner()
        cleaner.clean_params['max_window_size'] = window_size
        cleaner.clean_params['save_visualization'] = False  # 节省时间
        cleaner.clean_params['save_mask'] = False
        
        result = cleaner.process_fits_file(test_file)
        
        if result['success']:
            stats = result['statistics']
            regions = result['detected_regions']
            
            print(f"  检测到矩形噪点区域: {stats['num_noise_regions']} 个")
            print(f"  噪点像素: {stats['noise_count']} 个 ({stats['noise_ratio']:.3f}%)")
            
            # 统计不同大小的区域
            if regions:
                size_counts = {}
                for region in regions:
                    size = region['size']
                    size_counts[size] = size_counts.get(size, 0) + 1
                
                print(f"  大小分布: ", end="")
                for size in sorted(size_counts.keys()):
                    print(f"{size}像素:{size_counts[size]}个 ", end="")
                print()
        else:
            print(f"  处理失败: {result.get('error', '未知错误')}")

if __name__ == '__main__':
    if len(sys.argv) > 1:
        if sys.argv[1] == '--thresholds':
            test_different_thresholds()
        elif sys.argv[1] == '--windows':
            test_different_window_sizes()
        else:
            print("用法:")
            print("  python test_fast_rectangular.py              # 基本测试")
            print("  python test_fast_rectangular.py --thresholds # 测试不同阈值")
            print("  python test_fast_rectangular.py --windows    # 测试不同窗口大小")
    else:
        success = test_fast_rectangular()
        if not success:
            sys.exit(1)
