#!/usr/bin/env python3
"""
测试矩形噪点清理工具
"""

import os
import sys
from pathlib import Path
from rectangular_noise_cleaner import RectangularNoiseCleaner

def test_rectangular_noise_cleaner():
    """测试矩形噪点清理工具"""
    
    # 测试文件路径
    test_file = "noise2.fits"
    
    print("=" * 60)
    print("矩形噪点清理工具测试")
    print("=" * 60)
    print(f"测试文件: {test_file}")
    
    # 检查测试文件是否存在
    if not os.path.exists(test_file):
        print(f"错误: 测试文件不存在: {test_file}")
        return False
    
    # 创建清理器
    cleaner = RectangularNoiseCleaner()
    
    # 设置参数（针对矩形噪点优化）
    cleaner.clean_params.update({
        'zscore_threshold': 5.0,            # Z-score阈值（提高以减少误检）
        'max_noise_size': 9,                # 最大噪点大小
        'min_noise_size': 1,                # 最小噪点大小
        'rectangularity_threshold': 0.5,    # 矩形度阈值（降低以检测更多）
        'edge_sharpness_threshold': 0.2,    # 边缘锐度阈值（降低以检测更多）
        'morphology_kernel_size': 3,        # 形态学核大小
        'cleaning_method': 'adaptive',      # 清理方法
        'save_visualization': True,         # 保存可视化结果
        'save_mask': True,                 # 保存噪点掩码
        'save_analysis': True,             # 保存分析结果
    })
    
    print("\n参数设置:")
    for key, value in cleaner.clean_params.items():
        print(f"  {key}: {value}")
    
    print("\n" + "-" * 60)
    print("开始处理...")
    
    # 处理文件
    result = cleaner.process_fits_file(test_file)
    
    if result['success']:
        stats = result['statistics']
        print("\n" + "=" * 60)
        print("处理完成!")
        print("=" * 60)
        print(f"检测到矩形噪点: {stats['noise_count']} 个像素 ({stats['noise_ratio']:.3f}%)")
        print(f"矩形噪点区域: {stats['num_noise_regions']} 个")
        print(f"总像素数: {stats['total_pixels']:,}")
        
        print(f"\n原始图像统计:")
        print(f"  均值: {stats['original_stats']['mean']:.6f}")
        print(f"  中位数: {stats['original_stats']['median']:.6f}")
        print(f"  标准差: {stats['original_stats']['std']:.6f}")
        
        print(f"\n清理后图像统计:")
        print(f"  均值: {stats['cleaned_stats']['mean']:.6f}")
        print(f"  中位数: {stats['cleaned_stats']['median']:.6f}")
        print(f"  标准差: {stats['cleaned_stats']['std']:.6f}")
        
        print(f"\n变化统计:")
        print(f"  最大变化: {stats['changes']['max_change']:.6f}")
        print(f"  平均变化: {stats['changes']['mean_change']:.6f}")
        print(f"  噪点区域平均变化: {stats['changes']['noise_region_change']:.6f}")
        
        # 矩形噪点特征统计
        if stats['rectangular_noise_features']['size_stats']:
            size_stats = stats['rectangular_noise_features']['size_stats']
            print(f"\n噪点大小统计:")
            print(f"  最小: {size_stats['min']} 像素")
            print(f"  最大: {size_stats['max']} 像素")
            print(f"  平均: {size_stats['mean']:.1f} 像素")
            print(f"  中位数: {size_stats['median']:.1f} 像素")
        
        if stats['rectangular_noise_features']['rectangularity_stats']:
            rect_stats = stats['rectangular_noise_features']['rectangularity_stats']
            print(f"\n矩形度统计:")
            print(f"  最小: {rect_stats['min']:.3f}")
            print(f"  最大: {rect_stats['max']:.3f}")
            print(f"  平均: {rect_stats['mean']:.3f}")
            print(f"  中位数: {rect_stats['median']:.3f}")
        
        if stats['rectangular_noise_features']['edge_sharpness_stats']:
            edge_stats = stats['rectangular_noise_features']['edge_sharpness_stats']
            print(f"\n边缘锐度统计:")
            print(f"  最小: {edge_stats['min']:.3f}")
            print(f"  最大: {edge_stats['max']:.3f}")
            print(f"  平均: {edge_stats['mean']:.3f}")
            print(f"  中位数: {edge_stats['median']:.3f}")
        
        print(f"\n输出文件:")
        print(f"  清理后FITS: {result['cleaned_fits_file']}")
        if result['mask_fits_file']:
            print(f"  噪点掩码: {result['mask_fits_file']}")
        if result['visualization_file']:
            print(f"  可视化图: {result['visualization_file']}")
        if result['analysis_file']:
            print(f"  分析结果: {result['analysis_file']}")
        
        # 显示前几个检测到的噪点详情
        if result['analysis_results']:
            print(f"\n前5个检测到的矩形噪点详情:")
            print("-" * 80)
            print(f"{'ID':<4} {'大小':<6} {'矩形度':<8} {'边缘锐度':<10} {'边界框':<20}")
            print("-" * 80)
            for i, analysis in enumerate(result['analysis_results'][:5]):
                bbox = analysis['bbox']
                bbox_str = f"({bbox[0]}-{bbox[1]}, {bbox[2]}-{bbox[3]})"
                print(f"{i+1:<4} {analysis['size']:<6} {analysis['rectangularity']:<8.3f} "
                     f"{analysis['edge_sharpness']:<10.3f} {bbox_str:<20}")
        
        return True
    else:
        print(f"\n处理失败: {result.get('error', '未知错误')}")
        return False

def test_different_methods():
    """测试不同的清理方法"""
    
    test_file = "noise2.fits"
    
    if not os.path.exists(test_file):
        print(f"错误: 测试文件不存在: {test_file}")
        return
    
    methods = ['adaptive', 'median', 'gaussian', 'interpolation']
    
    print("=" * 60)
    print("测试不同矩形噪点清理方法")
    print("=" * 60)
    
    for method in methods:
        print(f"\n测试方法: {method}")
        print("-" * 40)
        
        cleaner = RectangularNoiseCleaner()
        cleaner.clean_params['cleaning_method'] = method
        cleaner.clean_params['save_visualization'] = True
        
        # 为不同方法创建不同的输出目录（在程序目录下）
        program_dir = Path(__file__).parent
        test_file_name = Path(test_file).stem
        output_dir = program_dir / f"rectangular_noise_cleaning_{method}_{test_file_name}"
        
        result = cleaner.process_fits_file(test_file, str(output_dir))
        
        if result['success']:
            stats = result['statistics']
            print(f"  ✓ 成功处理")
            print(f"  检测矩形噪点: {stats['noise_count']} 个像素")
            print(f"  矩形噪点区域: {stats['num_noise_regions']} 个")
            print(f"  平均变化: {stats['changes']['mean_change']:.6f}")
            print(f"  输出目录: {output_dir}")
        else:
            print(f"  ✗ 处理失败: {result.get('error', '未知错误')}")

def test_parameter_sensitivity():
    """测试参数敏感性"""
    
    test_file = "noise2.fits"
    
    if not os.path.exists(test_file):
        print(f"错误: 测试文件不存在: {test_file}")
        return
    
    print("=" * 60)
    print("测试参数敏感性")
    print("=" * 60)
    
    # 测试不同的矩形度阈值
    rectangularity_thresholds = [0.4, 0.6, 0.8]
    
    for threshold in rectangularity_thresholds:
        print(f"\n测试矩形度阈值: {threshold}")
        print("-" * 40)
        
        cleaner = RectangularNoiseCleaner()
        cleaner.clean_params['rectangularity_threshold'] = threshold
        cleaner.clean_params['save_visualization'] = False  # 节省时间
        cleaner.clean_params['save_mask'] = False
        cleaner.clean_params['save_analysis'] = False
        
        result = cleaner.process_fits_file(test_file)
        
        if result['success']:
            stats = result['statistics']
            print(f"  检测到矩形噪点区域: {stats['num_noise_regions']} 个")
            print(f"  噪点像素: {stats['noise_count']} 个")
        else:
            print(f"  处理失败: {result.get('error', '未知错误')}")

if __name__ == '__main__':
    if len(sys.argv) > 1:
        if sys.argv[1] == '--methods':
            test_different_methods()
        elif sys.argv[1] == '--sensitivity':
            test_parameter_sensitivity()
        else:
            print("用法:")
            print("  python test_rectangular_noise_cleaner.py           # 基本测试")
            print("  python test_rectangular_noise_cleaner.py --methods # 测试不同方法")
            print("  python test_rectangular_noise_cleaner.py --sensitivity # 测试参数敏感性")
    else:
        success = test_rectangular_noise_cleaner()
        if not success:
            sys.exit(1)
