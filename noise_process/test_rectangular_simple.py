#!/usr/bin/env python3
"""
简化的矩形噪点清理工具测试
使用更保守的参数进行快速测试
"""

import os
import sys
from pathlib import Path
from rectangular_noise_cleaner import RectangularNoiseCleaner

def test_simple():
    """简化测试"""
    
    # 测试文件路径
    test_file = "noise2.fits"
    
    print("=" * 60)
    print("矩形噪点清理工具 - 简化测试")
    print("=" * 60)
    print(f"测试文件: {test_file}")
    
    # 检查测试文件是否存在
    if not os.path.exists(test_file):
        print(f"错误: 测试文件不存在: {test_file}")
        return False
    
    # 创建清理器
    cleaner = RectangularNoiseCleaner()
    
    # 设置更保守的参数
    cleaner.clean_params.update({
        'zscore_threshold': 6.0,            # 更高的Z-score阈值
        'max_noise_size': 6,                # 更小的最大噪点大小
        'min_noise_size': 2,                # 更大的最小噪点大小
        'rectangularity_threshold': 0.7,    # 更高的矩形度阈值
        'edge_sharpness_threshold': 0.5,    # 更高的边缘锐度阈值
        'cleaning_method': 'median',        # 使用简单的中值滤波
        'save_visualization': True,         # 保存可视化结果
        'save_mask': True,                 # 保存噪点掩码
        'save_analysis': True,             # 保存分析结果
    })
    
    print("\n保守参数设置:")
    for key, value in cleaner.clean_params.items():
        print(f"  {key}: {value}")
    
    print("\n" + "-" * 60)
    print("开始处理...")
    
    # 处理文件
    result = cleaner.process_fits_file(test_file)
    
    if result['success']:
        stats = result['statistics']
        print("\n" + "=" * 60)
        print("处理完成!")
        print("=" * 60)
        print(f"检测到矩形噪点: {stats['noise_count']} 个像素 ({stats['noise_ratio']:.3f}%)")
        print(f"矩形噪点区域: {stats['num_noise_regions']} 个")
        
        if stats['rectangular_noise_features']['size_stats']:
            size_stats = stats['rectangular_noise_features']['size_stats']
            print(f"噪点大小: {size_stats['min']}-{size_stats['max']} 像素 (平均: {size_stats['mean']:.1f})")
        
        if stats['rectangular_noise_features']['rectangularity_stats']:
            rect_stats = stats['rectangular_noise_features']['rectangularity_stats']
            print(f"矩形度: {rect_stats['min']:.3f}-{rect_stats['max']:.3f} (平均: {rect_stats['mean']:.3f})")
        
        print(f"\n图像变化:")
        print(f"  最大变化: {stats['changes']['max_change']:.6f}")
        print(f"  平均变化: {stats['changes']['mean_change']:.6f}")
        print(f"  噪点区域平均变化: {stats['changes']['noise_region_change']:.6f}")
        
        print(f"\n输出文件:")
        print(f"  清理后FITS: {os.path.basename(result['cleaned_fits_file'])}")
        if result['mask_fits_file']:
            print(f"  噪点掩码: {os.path.basename(result['mask_fits_file'])}")
        if result['visualization_file']:
            print(f"  可视化图: {os.path.basename(result['visualization_file'])}")
        if result['analysis_file']:
            print(f"  分析结果: {os.path.basename(result['analysis_file'])}")
        
        # 显示检测到的噪点详情
        if result['analysis_results']:
            print(f"\n检测到的矩形噪点详情:")
            print("-" * 80)
            print(f"{'ID':<4} {'大小':<6} {'矩形度':<8} {'边缘锐度':<10} {'边界框':<20}")
            print("-" * 80)
            for i, analysis in enumerate(result['analysis_results']):
                bbox = analysis['bbox']
                bbox_str = f"({bbox[0]}-{bbox[1]}, {bbox[2]}-{bbox[3]})"
                print(f"{i+1:<4} {analysis['size']:<6} {analysis['rectangularity']:<8.3f} "
                     f"{analysis['edge_sharpness']:<10.3f} {bbox_str:<20}")
        
        return True
    else:
        print(f"\n处理失败: {result.get('error', '未知错误')}")
        return False

def test_command_line():
    """测试命令行接口"""
    print("\n" + "=" * 60)
    print("测试命令行接口")
    print("=" * 60)
    
    # 使用命令行方式运行
    import subprocess
    
    cmd = [
        "python", "rectangular_noise_cleaner.py",
        "--input", "noise2.fits",
        "--threshold", "6.0",
        "--max-size", "6",
        "--min-size", "2",
        "--rectangularity", "0.7",
        "--edge-sharpness", "0.5",
        "--method", "median"
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✓ 命令行测试成功")
            print("输出:")
            print(result.stdout)
        else:
            print("✗ 命令行测试失败")
            print("错误:")
            print(result.stderr)
            
    except subprocess.TimeoutExpired:
        print("✗ 命令行测试超时")
    except Exception as e:
        print(f"✗ 命令行测试异常: {str(e)}")

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == '--cmdline':
        test_command_line()
    else:
        success = test_simple()
        if not success:
            sys.exit(1)
