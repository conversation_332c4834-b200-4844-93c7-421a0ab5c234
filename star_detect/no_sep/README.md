# 快速星点检测器

这是一个高效的星点检测程序，专门用于处理FITS天文图像文件，不依赖SEP库。

## 功能特点

- 🚀 **快速高效**: 使用OpenCV进行快速图像处理
- 🎯 **精确检测**: 基于统计阈值的星点检测算法
- ⭕ **圆度过滤**: 专门检测圆形星点，过滤噪声和伪影
- 🌙 **暗星检测**: 专门优化的暗星检测模式，提升3倍检测能力
- 🔄 **自适应阈值**: 智能适应图像不同区域的亮度变化
- 📈 **对比度增强**: 直方图均衡化突出微弱星点
- 🔍 **形状分析**: 计算圆度、实心度、长宽比等形状指标
- 🌈 **颜色编码**: 根据圆度用不同颜色标记星点
- 📊 **详细报告**: 生成JSON和文本格式的检测报告
- 🖼️ **可视化输出**: 在JPG图像中标记检测到的星点位置
- 📝 **完整日志**: 记录处理过程和错误信息

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. 确保FITS文件位于 `E:\fix_data\star-detect` 目录
2. 运行检测程序：

```bash
python detect_stars.py
```

## 输出文件

程序会在 `output_images` 目录下生成：

- `*_stars.jpg`: 标记了星点的图像文件
- `detection_summary.json`: JSON格式的检测报告
- `detection_summary.txt`: 文本格式的检测报告
- `star_detection.log`: 处理日志文件

## 参数调整

可以在 `detect_stars.py` 中调整以下参数：

```python
# 暗星检测模式（当前设置）
detector = StarDetector(
    min_area=3,             # 最小星点面积（像素）
    max_area=1000,          # 最大星点面积（像素）
    threshold_factor=2.0,   # 阈值因子（越小检测越敏感）
    min_circularity=0.3,    # 最小圆度（0-1，越大越圆）
    min_solidity=0.5,       # 最小实心度（0-1，越大越实心）
    adaptive_threshold=True, # 自适应阈值
    dark_star_mode=True     # 暗星检测模式
)

# 标准检测模式
detector = StarDetector(
    min_area=5,
    max_area=1000,
    threshold_factor=3.0,
    min_circularity=0.4,
    min_solidity=0.6,
    adaptive_threshold=False,
    dark_star_mode=False
)
```

### 圆度参数说明

- **min_circularity**: 圆度阈值，范围0-1
  - 0.8-1.0: 非常圆的星点
  - 0.5-0.8: 较圆的星点
  - 0.3-0.5: 一般圆的星点
  - 建议值: 0.4（过滤掉明显非圆形的对象）

- **min_solidity**: 实心度阈值，范围0-1
  - 接近1.0: 完全实心的对象
  - 0.6-0.9: 较实心的对象
  - 建议值: 0.6（过滤掉有孔洞的伪影）

## 算法说明

1. **图像预处理**: 使用sigma-clipped统计计算背景噪声
2. **阈值计算**: 基于背景统计的自适应阈值
3. **二值化**: 将图像转换为二值图像
4. **形态学操作**: 去除噪声和伪影
5. **轮廓检测**: 使用OpenCV查找连通区域
6. **面积过滤**: 根据面积范围过滤候选星点
7. **形状分析**: 计算圆度、实心度、长宽比
8. **圆度过滤**: 只保留足够圆的星点
9. **质心计算**: 计算每个星点的精确位置

### 圆度计算公式

- **圆度**: `4π × 面积 / 周长²`
  - 完美圆形 = 1.0
  - 椭圆或不规则形状 < 1.0

- **实心度**: `轮廓面积 / 凸包面积`
  - 完全实心 = 1.0
  - 有孔洞或凹陷 < 1.0

### 颜色编码

输出图像中星点的颜色表示其圆度：
- 🟢 **绿色**: 圆度 ≥ 0.8（非常圆）
- 🟡 **黄色**: 圆度 0.5-0.8（较圆）
- 🟠 **橙色**: 圆度 < 0.5（一般圆）

## 性能优势

相比传统方法，本程序具有以下优势：

- 不依赖SEP库，减少安装复杂度
- 使用OpenCV优化的算法，处理速度快
- 内存占用低，适合批量处理
- 参数简单，易于调整和优化

## 故障排除

如果遇到问题，请检查：

1. FITS文件路径是否正确
2. 依赖包是否正确安装
3. 查看 `star_detection.log` 文件获取详细错误信息
